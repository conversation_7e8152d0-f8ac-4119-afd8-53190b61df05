package lib

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJoinURLRequest(t *testing.T) {
	t.Run("Should link with /", func(t *testing.T) {
		result := JoinURL("http://google.com", "end")
		assert.Equal(t, "http://google.com/end", result)
	})
	t.Run("Should link with only 1 /", func(t *testing.T) {
		result := JoinURL("http://google.com/", "/end")
		assert.Equal(t, "http://google.com/end", result)
	})
	t.Run("Should link with directly", func(t *testing.T) {
		result := JoinURL("http://google.com", "/end")
		assert.Equal(t, "http://google.com/end", result)
		result = JoinURL("http://google.com/", "end")
		assert.Equal(t, "http://google.com/end", result)
	})
}

func TestURI(t *testing.T) {
	result := URI("coremedia", "article", "123")
	assert.Equal(t, "coremedia://article/123", result)
}

func TestTransformValueToPointer(t *testing.T) {
	t.Run("should not be changed by original var", func(t *testing.T) {
		foo := 1
		bar := IntPtr(foo)
		foo = 2
		assert.Equal(t, 1, *bar)
	})

	t.Run("should not affect each other", func(t *testing.T) {
		val := 1
		foo := IntPtr(val)
		bar := IntPtr(val)
		val = 2
		assert.Equal(t, 1, *foo)
		assert.Equal(t, 1, *bar)

		*foo = 3
		assert.Equal(t, 3, *foo)
		assert.Equal(t, 1, *bar)
	})

	t.Run("should not be changed by original var", func(t *testing.T) {
		foo := true
		bar := BoolPtr(foo)
		foo = false
		assert.True(t, *bar)
	})

	t.Run("should not affect each other", func(t *testing.T) {
		val := true
		foo := BoolPtr(val)
		bar := BoolPtr(val)
		val = false
		assert.True(t, *foo)
		assert.True(t, *bar)

		*foo = false
		assert.False(t, *foo)
		assert.True(t, *bar)
	})
}

func TestProcessed(t *testing.T) {
	p := make(Processed)
	t.Run("should add value in correctly", func(t *testing.T) {
		p.AddValue([]string{"1", "1-1", "1-1-1"}, []byte("value1"))
		v, ok := p.Value("1", "1-1", "1-1-1")
		assert.Equal(t, "value1", string(v))
		assert.True(t, ok)
	})

	t.Run("should not affect each other under sub level", func(t *testing.T) {
		p.AddValue([]string{"1", "1-1", "1-1-2"}, []byte("value2"))
		v, ok := p.Value("1", "1-1", "1-1-1")
		assert.Equal(t, "value1", string(v))
		assert.True(t, ok)
		v, ok = p.Value("1", "1-1", "1-1-2")
		assert.Equal(t, "value2", string(v))
		assert.True(t, ok)
	})
}

func TestTTLRange(t *testing.T) {
	t.Run("Should return 270 and 330 for 300 base TTL tolerance 10", func(t *testing.T) {
		min, max := GetTTLRangeInSec(300, 10)
		assert.Equal(t, 270, min)
		assert.Equal(t, 330, max)
	})
	t.Run("Should return 0 and 600 for 300 base TTL tolerance greater than or equal 100", func(t *testing.T) {
		min, max := GetTTLRangeInSec(300, 100)
		assert.Equal(t, 0, min)
		assert.Equal(t, 600, max)
		min, max = GetTTLRangeInSec(300, 105)
		assert.Equal(t, 0, min)
		assert.Equal(t, 600, max)
		min, max = GetTTLRangeInSec(300, 10500)
		assert.Equal(t, 0, min)
		assert.Equal(t, 600, max)
	})
	t.Run("Should return 297 and 303 for 300 base TTL tolerance less than or equal 1", func(t *testing.T) {
		min, max := GetTTLRangeInSec(300, 0)
		assert.Equal(t, 297, min)
		assert.Equal(t, 303, max)
		min, max = GetTTLRangeInSec(300, 0.5)
		assert.Equal(t, 297, min)
		assert.Equal(t, 303, max)
		min, max = GetTTLRangeInSec(300, 1)
		assert.Equal(t, 297, min)
		assert.Equal(t, 303, max)
	})
}

func TestTTLRandom(t *testing.T) {
	t.Run("Should generate a numer between 270 and 330 for 300 base TTL tolerance 10", func(t *testing.T) {
		ttl := GenTTLRandom(300, 10).Seconds()
		assert.GreaterOrEqual(t, ttl, float64(270))
		assert.LessOrEqual(t, ttl, float64(330))
	})
	t.Run("Should use tolerance 100 if more than 100 is passed in", func(t *testing.T) {
		ttl := GenTTLRandom(300, 1000).Seconds()
		assert.GreaterOrEqual(t, ttl, float64(0))
		assert.LessOrEqual(t, ttl, float64(600))
	})

	t.Run("Should use tolerance 1 if less than 1 s passed in", func(t *testing.T) {
		ttl := GenTTLRandom(300, -10).Seconds()
		assert.GreaterOrEqual(t, ttl, float64(297))
		assert.LessOrEqual(t, ttl, float64(303))
	})

	t.Run("Should generate a numer between 270 and 330 for 300 base TTL tolerance 10", func(t *testing.T) {
		ttl := GenTTLRandomSeconds(300, 10)
		assert.GreaterOrEqual(t, ttl, 270)
		assert.LessOrEqual(t, ttl, 330)
	})
	t.Run("Should use tolerance 100 if more than 100 is passed in", func(t *testing.T) {
		ttl := GenTTLRandomSeconds(300, 1000)
		assert.GreaterOrEqual(t, ttl, 0)
		assert.LessOrEqual(t, ttl, 600)
	})

	t.Run("Should use tolerance 1 if less than 1 s passed in", func(t *testing.T) {
		ttl := GenTTLRandomSeconds(300, -10)
		assert.GreaterOrEqual(t, ttl, 297)
		assert.LessOrEqual(t, ttl, 303)
	})
}

func TestTerminusIngesterError(t *testing.T) {
	t.Run("should parse data to TerminusIngesterError", func(t *testing.T) {
		te := TerminusIngesterError{
			Err: fmt.Errorf("general error"),
		}
		b, _ := json.Marshal(te)
		err := ParseTerminusIngesterError(b)
		assert.Error(t, err)
		assert.Equal(t, "general error", err.Error())
	})
	t.Run("should parse empty TerminusIngesterError", func(t *testing.T) {
		te := TerminusIngesterError{}
		b, _ := json.Marshal(te)
		err := ParseTerminusIngesterError(b)
		assert.Error(t, err)
	})
}
