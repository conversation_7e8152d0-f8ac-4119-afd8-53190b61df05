package cache

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang/snappy"
	"github.com/redis/go-redis/v9"
	log "github.com/sirupsen/logrus"

	base "stash.abc-dev.net.au/ter/lib"
)

var ctx = context.Background()

// RedisClient is the redis cache service client
type RedisClient struct {
	c           redis.UniversalClient
	config      Config // flag indicate if compress value when set value
	isCluster   bool
	nodeClients base.Expirable[[]string]
}

// NewRedisClient function is for create a new client with given provider
func NewRedisClient(config Config, cluster bool, servers ...string) (*RedisClient, error) {
	client := &RedisClient{
		isCluster: cluster,
	}

	if cluster {
		client.c = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:           servers,
			ConnMaxIdleTime: config.IdleTimeout,
			ConnMaxLifetime: config.MaxConnAge,
			MaxRetries:      config.MaxRetries,
			PoolSize:        config.PoolSize,
			PoolTimeout:     config.PoolTimeout,
			ReadTimeout:     config.ReadTimeout,
			WriteTimeout:    config.WriteTimeout,
		})
	} else {
		client.c = redis.NewClient(&redis.Options{
			Addr:            servers[0],
			ConnMaxIdleTime: config.IdleTimeout,
			ConnMaxLifetime: config.MaxConnAge,
			MaxRetries:      config.MaxRetries,
			PoolSize:        config.PoolSize,
			PoolTimeout:     config.PoolTimeout,
			ReadTimeout:     config.ReadTimeout,
			WriteTimeout:    config.WriteTimeout,
		})
	}
	_, err := client.c.Ping(ctx).Result()
	if err != nil {
		return nil, err
	}

	client.config = config

	return client, nil
}

// set key and value to cache
// @param key redis key
// @param value   value
// @param ttl expire time duration, 0 means never expire
func (c *RedisClient) set(key string, value []byte, ttl time.Duration) error {
	err := c.c.Set(ctx, key, value, ttl).Err()
	if err != nil {
		return err
	}
	return nil
}

// get value of specified key from cache
// @param key redis key
func (c *RedisClient) get(key string) ([]byte, error) {
	result, err := c.c.Get(ctx, key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return nil, base.ErrNotFound
		}
		return nil, err
	}
	return []byte(result), nil
}

// Delete is for delete key
func (c *RedisClient) Delete(key string) error {
	_, err := c.c.Del(ctx, key).Result()
	return err
}

// nodes return all master nodes of current cluster.
// It will return nil if current client is not connect to a Redis cluster
func (c *RedisClient) nodes() ([]string, error) {
	if c.isCluster {
		var masters []string
		shards, err := c.c.ClusterNodes(ctx).Result()
		if err != nil {
			log.WithFields(log.Fields{
				"callingFn": "NodeClients()",
			}).WithError(err).Errorf("Error fetching cluster nodes")
			return nil, err
		}

		masters, err = parseClusterMasterNodes(shards)
		if err != nil || len(masters) == 0 {
			log.WithFields(log.Fields{
				"callingFn": "NodeClients()",
				"shards":    shards,
			}).WithError(err).Errorf("Error parsing cluster masters")

			return nil, err
		}
		return masters, nil
	}
	return nil, nil
}

func (c *RedisClient) deleteByPattern(pattern string) error {
	var err error
	wg := &sync.WaitGroup{}
	if c.isCluster {
		//TER-1997 - moving around cluster node logic to here to minimise the number of cluster node invokes
		//Probably refactor this later to be set as an env variable
		masters, err := c.nodes()
		if err != nil {
			return err
		}
		for _, master := range masters {
			node := master
			go func() {
				nodeClient := redis.NewClient(&redis.Options{
					Addr: node,
				})
				delErr := deleteByPattern(nodeClient, pattern, wg)
				if delErr != nil {
					err = delErr

					log.WithFields(log.Fields{
						"callingFn": "DeleteByPattern()",
						"key":       pattern,
					}).WithError(delErr).Errorf("Error when single deleting by pattern:  %s", pattern)
				}
			}()
		}
	} else {
		err = deleteByPattern(c.c, pattern, wg)
	}
	wg.Wait()
	return err
}

func deleteByPattern(client redis.Cmdable, pattern string, wg *sync.WaitGroup) error {
	var cursor uint64
	//var n int
	var err error
	for {
		var keys []string

		//SCAN iterator is less elegant than using KEYS, but KEYS has poor performance on production systems.
		keys, cursor, err = client.Scan(ctx, cursor, pattern, 300).Result()
		if err != nil {
			log.WithFields(log.Fields{
				"callingFn": "deleteByPattern()",
				"pattern":   pattern,
			}).WithError(err).Errorf("Error when scanning by pattern %s", pattern)
		}

		if len(keys) > 0 {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for _, key := range keys {
					_, delErr := client.Del(ctx, key).Result()
					if delErr != nil {
						err = delErr

						log.WithFields(log.Fields{
							"callingFn": "deleteByPattern()",
							"pattern":   pattern,
						}).WithError(err).Errorf("Error when deleting key %v", key)
					}
				}
			}()
		}

		if cursor == 0 {
			break
		}
	}

	//fmt.Println("Scan Found this many keys: ", n)

	return nil
}

// Info returns cache client info
func (c *RedisClient) Info() (string, error) {
	info := c.c.Info(ctx)
	if info.Err() != nil {
		return "", info.Err()
	}
	return info.Val(), nil
}

// setHash is to set a map to a key in cache.
// @param key redis key
// @param m   key value hash of the value
// @param ttl expire time duration, 0 means never expire // deprecated 0(never expire) to avoid -1 issue
func (c *RedisClient) setHash(key string, m map[string][]byte, ttl time.Duration) error {
	m1 := make(map[string]interface{})
	for field, value := range m {
		m1[field] = value
	}
	_, err := c.c.HSet(ctx, key, m1).Result()
	if err != nil {
		return err
	}
	if ttl < MinTTL {
		ttl = MinTTL
	}
	_, err = c.c.Expire(ctx, key, ttl).Result()
	if err != nil {
		return err
	}
	return nil
}

// getHash is to get whole hash from specified key
func (c *RedisClient) getHash(key string) (map[string][]byte, error) {
	m, err := c.c.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}
	if len(m) == 0 {
		return nil, base.ErrNotFound
	}
	result := make(map[string][]byte)
	for k, v := range m {
		result[k] = []byte(v)
	}
	return result, nil
}

// getHashWithTTL is to get whole hash from specified key
func (c *RedisClient) getHashWithTTL(key string) (map[string][]byte, time.Duration, error) {
	m, err := c.c.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, 0, err
	}
	if len(m) == 0 {
		return nil, TTLKeyNotExist, base.ErrNotFound
	}
	ttl, err := c.c.TTL(ctx, key).Result()
	if err != nil {
		return nil, TTLKeyNotExist, err
	}
	result := make(map[string][]byte)
	for k, v := range m {
		result[k] = []byte(v)
	}
	return result, ttl, nil
}

// getHashFields by fields
func (c *RedisClient) getHashFields(key string, fields ...string) (map[string][]byte, error) {
	values, err := c.c.HMGet(ctx, key, fields...).Result()
	if err != nil {
		return nil, err
	}
	m := make(map[string][]byte)
	for i, field := range fields {
		if values[i] == nil {
			continue
		}
		m[field] = []byte(values[i].(string))
	}
	return m, nil
}

// getHashFieldsWithTTL by fields
func (c *RedisClient) getHashFieldsWithTTL(key string, fields ...string) (map[string][]byte, time.Duration, error) {
	m := make(map[string][]byte)
	values, err := c.c.HMGet(ctx, key, fields...).Result()
	if err != nil {
		return nil, 0, err
	}
	for i, field := range fields {
		if values[i] == nil {
			continue
		}
		m[field] = []byte(values[i].(string))
	}
	if len(m) == 0 {
		return m, TTLKeyNotExist, nil
	}
	ttl, err := c.c.TTL(ctx, key).Result()
	if err != nil {
		return nil, 0, err
	}
	return m, ttl, nil
}

// SetTTL is the function to set TTL of a key
func (c *RedisClient) SetTTL(key string, ttl time.Duration) error {
	return c.c.Expire(ctx, key, ttl).Err()
}

// TTL is the function to get TTL of a key
func (c *RedisClient) TTL(key string) (time.Duration, error) {
	result := c.c.TTL(ctx, key)
	return result.Result()
}

// Close connection
func (c *RedisClient) Close() error {
	return c.c.Close()
}

// Store doc into cache
func (c *RedisClient) Store(key string, content []byte, meta Meta) error {
	if c.c == nil {
		return nil
	}
	data := make(map[string][]byte)
	data["doc_type"] = []byte(meta.DocType)
	if content != nil {
		if c.config.Compress {
			var buf []byte
			data["content"] = snappy.Encode(buf, content)
			data["compress"] = []byte(compressSnappy)
		} else {
			data["content"] = content
			data["compress"] = []byte(compressNone)
		}
	}
	// Save Processed content
	if meta.Processed != nil {
		data["processed"] = meta.Processed
	}

	if meta.Err.HasError() {
		data["err"], _ = json.Marshal(meta.Err)
	} else {
		data["err"] = []byte("")
	}
	if meta.Version > 0 {
		data["version"] = []byte(strconv.Itoa(meta.Version))
	} else {
		data["version"] = []byte("")
	}
	return c.setHash(key, data, meta.TTL)
}

// Keys is to get all keys by pattern
func (c *RedisClient) Keys(key string) ([]string, error) {
	return c.c.Keys(ctx, key).Result()
}

// GetContent by key
func (c *RedisClient) GetContent(key string) (Meta, []byte, error) {
	return c.getFields(key, false, "content", "doc_type", "err", "compress", "processed", "version")
}

// GetContentWithTTL returns data with TTL
func (c *RedisClient) GetContentWithTTL(key string) (Meta, []byte, error) {
	return c.getFields(key, true, "content", "doc_type", "err", "compress", "processed", "version")
}

// GetMeta returns meta data
func (c *RedisClient) GetMeta(key string, withTTL bool) (Meta, error) {
	meta, _, err := c.getFields(key, withTTL, "doc_type", "err", "compress", "processed", "version")
	return meta, err
}

func (c *RedisClient) getFields(key string, withTTL bool, fields ...string) (Meta, []byte, error) {
	if c.c == nil {
		return Meta{}, nil, base.ErrNotFound
	}
	var result map[string][]byte
	var ttl time.Duration
	var err error
	if withTTL {
		result, ttl, err = c.getHashFieldsWithTTL(key, fields...)
	} else {
		result, err = c.getHashFields(key, fields...)
	}
	meta := makeMeta(result, ttl)
	if err != nil {
		return meta, nil, err
	}
	if len(result) == 0 {
		return meta, nil, base.ErrNotFound
	}
	if !meta.Err.HasError() {
		content, ok := result["content"]
		if !ok {
			return meta, nil, nil
		}
		if compress, ok := result["compress"]; ok && string(compress) == compressSnappy {
			var dst []byte
			buf, err := snappy.Decode(dst, content)
			if err != nil {
				return meta, nil, err
			}
			return meta, buf, nil
		}
		return meta, content, nil
	}
	return meta, nil, meta.Err
}

// DeleteAllRelatedKeys Deletes specified key and all other keys based on specified key with parameters.
// enrichedFunc is for generate enriched keys. It will delete enriched key as well if enrichedFunc is not nil.
func (c *RedisClient) DeleteAllRelatedKeys(key string, enrichedFunc func(string) []string) error {
	if c.c == nil {
		return nil
	}
	c.Delete(key)
	pattern := key + "::*"   //:: for safety. E.g. if we're deleting capi::100, we don't want to also delete capi::100* test
	if enrichedFunc != nil { // if enriched func is not nil will need to delete enriched key as well
		for _, enrichedKey := range enrichedFunc(key) {
			c.Delete(enrichedKey)
		}
		pattern = "*" + pattern
	}
	return c.DeleteByPattern(pattern)
}

func (c *RedisClient) DeleteByPattern(pattern string) error {
	if c.c == nil {
		return nil
	}
	if !c.isCluster {
		return c.deleteByPattern(pattern)
	}
	return c.deleteByPatternAcrossCluster(pattern)
}

func (c *RedisClient) deleteByPatternAcrossCluster(pattern string) error {
	if c.c == nil {
		return nil
	}
	if c.nodeClients.Expired() {
		nodes, err := c.nodes()
		if err != nil {
			return err
		}
		c.nodeClients.Update(nodes, time.Minute)
	}
	for _, node := range c.nodeClients.Value {
		go func(node string) {
			nodeClient, err := NewRedisClient(c.config, false, node)
			if err != nil {
				return
			}
			defer nodeClient.Close()
			fmt.Println("==== deleteByPattern node", node, pattern)
			nodeClient.deleteByPattern(pattern)
		}(node)
	}
	return nil
}

// parseClusterNodes grabs the text output of 'cluster nodes' and extracts master node IP:host addresses from the text
// shamefully stolen from tools/cmd/redis-cleanup!
func parseClusterMasterNodes(buf string) ([]string, error) {
	servers := make([]string, 0)

	s := bufio.NewScanner(strings.NewReader(buf))
	for s.Scan() {
		if strings.Contains(s.Text(), "master") {
			line := strings.Split(s.Text(), " ")
			if len(line) > 0 {
				server := strings.Split(line[1], "@")[0]
				servers = append(servers, server)
			}
		}
	}

	if err := s.Err(); err != nil {
		return nil, err
	}

	return servers, nil
}
