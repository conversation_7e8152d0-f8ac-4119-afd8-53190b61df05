package cache

import (
	"time"
)

// Defines Redis default TTL values
const (
	TTLKeyNotExist = time.Duration(-2)
	TTLNotSet      = time.Duration(-1)
)

const (
	MinTTL = 1 * time.Second
)

type CacheType int

const (
	Redis CacheType = iota
	Passthrough
)

type Client interface {
	Store(key string, content []byte, meta Meta) error
	Keys(key string) ([]string, error)
	GetContent(key string) (Meta, []byte, error)
	GetContentWithTTL(key string) (Meta, []byte, error)
	GetMeta(key string, withTTL bool) (Meta, error)
	Info() (string, error)
	Delete(key string) error
	DeleteByPattern(patter string) error
	DeleteAllRelatedKeys(key string, enrichedFunc func(string) []string) error
	SetTTL(key string, ttl time.Duration) error
	TTL(key string) (time.Duration, error)
	Close() error
}

// Config is for passing cache client configuration.
// In this way it can prevent accident change after client initial.
type Config struct {
	PoolSize int
	// Maximum number of retries before giving up.
	// Default is to not retry failed commands.
	MaxRetries int
	// Timeout for socket reads. If reached, commands will fail
	// with a timeout instead of blocking. Use value -1 for no timeout and 0 for default.
	// Default is 3 seconds.
	ReadTimeout time.Duration
	// Timeout for socket writes. If reached, commands will fail
	// with a timeout instead of blocking.
	// Default is ReadTimeout.
	WriteTimeout time.Duration
	// Connection age at which client retires (closes) the connection.
	// Default is to not close aged connections.
	MaxConnAge time.Duration
	// Amount of time client waits for connection if all connections
	// are busy before returning an error.
	// Default is ReadTimeout + 1 second.
	PoolTimeout time.Duration
	// Amount of time after which client closes idle connections.
	// Should be less than server's timeout.
	// Default is 5 minutes. -1 disables idle timeout check.
	IdleTimeout time.Duration
	// Frequency of idle checks made by idle connections reaper.
	// Default is 1 minute. -1 disables idle connections reaper,
	// but idle connections are still discarded by the client
	// if IdleTimeout is set.
	IdleCheckFrequency time.Duration

	Type CacheType

	Compress bool
}
