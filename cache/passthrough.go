package cache

import "time"

// PassthroughClient is the empty client
type PassthroughClient struct {
}

// NewPassthroughClient function is for create a new empty client
func NewPassthroughClient() *PassthroughClient {
	return &PassthroughClient{}
}
func (c *PassthroughClient) Store(key string, content []byte, meta Meta) error {
	return nil
}
func (c *PassthroughClient) Keys(key string) ([]string, error) {
	return nil, nil
}
func (c *PassthroughClient) GetContent(key string) (Meta, []byte, error) {
	return Meta{}, nil, nil
}
func (c *PassthroughClient) GetContentWithTTL(key string) (Meta, []byte, error) {
	return Meta{}, nil, nil
}
func (c *PassthroughClient) GetMeta(key string, withTTL bool) (Meta, error) {
	return Meta{}, nil
}
func (c *PassthroughClient) Info() (string, error) {
	return "", nil
}
func (c *PassthroughClient) Delete(key string) error {
	return nil
}
func (c *PassthroughClient) DeleteByPattern(pattern string) error {
	return nil
}
func (c *PassthroughClient) DeleteAllRelatedKeys(key string, enrichedFunc func(string) []string) error {
	return nil
}
func (c *PassthroughClient) SetTTL(key string, ttl time.Duration) error {
	return nil
}
func (c *PassthroughClient) TTL(key string) (time.Duration, error) {
	return 0, nil
}
func (c *PassthroughClient) Close() error {
	return nil
}
