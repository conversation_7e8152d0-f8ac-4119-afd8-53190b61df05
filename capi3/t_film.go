package capi3

// Film is a top level Film document returned from CAPI
type Film struct {
	CMEntity

	Director          string          `json:"director,omitempty"`
	Distributor       string          `json:"distributor,omitempty"`
	LocationsDepicted string          `json:"locationsDepicted,omitempty"`
	ReleaseNotes      *RichTextObject `json:"releaseNotes,omitempty"`
	RunningTime       *int            `json:"runningTime,omitempty"`
}

// Process converts data to Terminus required formats
func (c *Film) Process() {
	c.CMEntity.Process()
	c.ReleaseNotes.ToFMLWithMeta(c.Meta())
}

func (Film) IsCapi() {}
