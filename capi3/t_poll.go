package capi3

// Poll is a top level ABCAudio document returned from CAPI
type Poll struct {
	CMTeasable

	Label       string          `json:"label,omitempty"`
	Question    string          `json:"question,omitempty"`
	Description *RichTextObject `json:"description,omitempty"`
	Explanation *RichTextObject `json:"explanation,omitempty"`
	CloseDate   string          `json:"closeDate,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	IsOpen      bool            `json:"isOpen,omitempty"`
}

// Process converts data to Terminus required formats
func (a *Poll) Process() {
	a.CMTeasable.Process()
	a.Explanation.ToFMLWithMeta(a.Meta())
	a.Description.ToFMLWithMeta(a.Meta())
}

func (Poll) IsCapi() {}

type AnswerItem struct {
	CMTeasable

	AnswerText string `json:"answerText,omitempty"`
	Value      string `json:"value,omitempty"`
}

func (a *AnswerItem) Process() {
	a.CMTeasable.Process()
}

func (AnswerItem) IsCapi() {}
