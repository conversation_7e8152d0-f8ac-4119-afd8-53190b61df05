package capi3

// Gallery is a top level Gallery document returned from CAPI
type Gallery struct {
	CMCollection

	Description      *RichTextObject `json:"description,omitempty"`
	TransitionPauses string          `json:"transitionPauses,omitempty"`
}

// Process converts data to Terminus required formats
func (c *Gallery) Process() {
	c.CMCollection.Process()
	c.Description.ToFMLWithMeta(c.Meta())
}

func (Gallery) IsCapi() {}
