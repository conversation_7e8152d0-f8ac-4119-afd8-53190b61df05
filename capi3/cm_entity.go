package capi3

// CMEntity is a top level ABCEntity document returned from CAPI
type CMEntity struct {
	CMTeasable

	Description *RichTextObject `json:"description,omitempty"`
	Homepage    string          `json:"homepage,omitempty"`
	Released    string          `json:"released,omitempty"`
	Wikipedia   string          `json:"wikipedia,omitempty"`
}

// Process converts data to Terminus required formats
func (a *CMEntity) Process() {
	a.CMTeasable.Process()
	a.Description.ToFMLWithMeta(a.Meta())
}
