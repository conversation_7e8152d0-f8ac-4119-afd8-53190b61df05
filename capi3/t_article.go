package capi3

// Article contains Article specific document fields
type Article struct {
	CMTeasable

	Subject string          `json:"subject,omitempty"`
	Text    *RichTextObject `json:"text,omitempty"`
	Summary *RichTextObject `json:"summary,omitempty"`
}

// Process converts data to Terminus required formats
func (a *Article) Process() {
	a.CMTeasable.Process()
	a.Text.ToFMLWithMeta(a.Meta())
	a.Summary.ToFMLWithMeta(a.Meta())
}

func (Article) IsCapi() {}
