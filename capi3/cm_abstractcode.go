package capi3

// CMAbstractCode contains content shared by all CMAbstractCode Capi documents
type CMAbstractCode struct {
	CMLocalized

	Code        *RichTextObject `json:"code,omitempty"`
	Description string          `json:"description,omitempty"`
	URL         string          `json:"url,omitempty"`
}

// Process converts data to Terminus required formats
func (c *CMAbstractCode) Process() {
	c.Code.ToFMLWithMeta(c.Meta())
}

// Valid returns valid status base on ValidTo and current time
func (l *CMAbstractCode) Valid() ValidStatus {
	return Valid
}
