package capi3

// Program is a top level Program document returned from CAPI
type Program struct {
	CMTeasable

	Description *RichTextObject `json:"description,omitempty"`

	// New fields from Horizon 2
	AirportCode    string `json:"airportCode,omitempty"`
	IsDigitalFirst bool   `json:"isDigitalFirst,omitempty"`
	IOSAppID       string `json:"iosAppId,omitempty"`
	AndroidAppID   string `json:"androidAppId,omitempty"`
	AvailableFrom  string `json:"availableFrom,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	AvailableTo    string `json:"availableTo,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
}

// Process converts data to Terminus required formats
func (c *Program) Process() {
	c.CMTeasable.Process()
	c.Description.ToFMLWithMeta(c.Meta())
}

func (Program) IsCapi() {}
