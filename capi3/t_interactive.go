package capi3

// Interactive is a top level Interactive document returned from CAPI
type Interactive struct {
	CMTeasable

	AspectRatio string          `json:"aspectRatio,omitempty"`
	EmbedCode   *RichTextObject `json:"embedCode,omitempty"`
	EmbedURL    string          `json:"embedUrl,omitempty"`
	ResourceID  string          `json:"resourceId,omitempty"`
}

// Process converts data to Terminus required formats
func (c *Interactive) Process() {
	c.CMTeasable.Process()
	c.EmbedCode.ToFMLWithMeta(c.Meta())
}

func (Interactive) IsCapi() {}
