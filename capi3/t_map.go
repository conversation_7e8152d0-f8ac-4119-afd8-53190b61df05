package capi3

// Map contains Map specific document fields
type Map struct {
	CMTeasable

	DataURL   string          `json:"dataUrl,omitempty"`
	EmbedCode *RichTextObject `json:"embedCode,omitempty"`
	ZoomLevel *int            `json:"zoomLevel,omitempty"`
}

// Process converts data to Terminus required formats
func (a *Map) Process() {
	a.CMTeasable.Process()
	a.EmbedCode.ToFMLWithMeta(a.Meta())
}

func (Map) IsCapi() {}
