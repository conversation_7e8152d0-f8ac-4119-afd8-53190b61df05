package capi3

// Image is a top level ABCAudio document returned from CAPI
type Image struct {
	CMImage
	BinaryKey    string                  `json:"binaryKey,omitempty"`
	CropInfo     map[string][]*RatioInfo `json:"cropInfo,omitempty"`
	OriginalInfo *ImageOriginalInfo      `json:"originalInfo,omitempty"`
}

// Process converts data to Terminus required formats
func (a *Image) Process() {
	a.CMImage.Process()
}

func (Image) IsCapi() {}

type ImageOriginalInfo struct {
	Width     int    `json:"width,omitempty"`
	Height    int    `json:"height,omitempty"`
	Extension string `json:"extension,omitempty"`
	URL       string `json:"url,omitempty"`
}
