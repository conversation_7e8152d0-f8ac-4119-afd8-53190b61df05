package capi3

import (
	"strings"
)

type ValidStatus int

const (
	Valid   ValidStatus = 0
	Expired ValidStatus = 1
)

// Processor is an interface to a CAPI struct to enable normalisation of content
type Processor interface {
	Process()
	IsCapi()
	ContentID() string
	ContentType() string
	ContentSource() string
	URI() string
	Valid() ValidStatus
	GetVersion() int
	GetObject() CMObject
	GetRegions() []string
	SetRegions(regions []string)
}

// Valuer is an interface for doctype that returns simple field as a value
// eg. Description in Symbol and Name in ViewType
type Valuer interface {
	SimpleValue() string
}

// Model returns the mapped Model to use for Capi3
func Model(doctype string) Processor {
	switch strings.ToLower(doctype) {
	case ArticleEntity:
		return &Article{}
	case AudioEntity:
		return &Audio{}
	case AudioEpisodeEntity:
		return &AudioEpisode{}
	case AudioSegmentEntity:
		return &AudioSegment{}
	case BookEntity:
		return &Book{}
	case ChannelEntity:
		return &Channel{}
	case CollectionEntity:
		return &Collection{}
	case CssCodeEntity:
		return &CssCode{}
	case DownloadObjectEntity:
		return &DownloadObject{}
	case DynamicCollectionEntity:
		return &DynamicCollection{}
	case ExternalLinkEntity:
		return &ExternalLink{}
	case FeedEntity:
		return &Feed{}
	case FilmEntity:
		return &Film{}
	case GalleryEntity:
		return &Gallery{}
	case GameEntity:
		return &Game{}
	case GroupEntity:
		return &Group{}
	case HTMLFragmentEntity:
		return &HTMLFragment{}
	case ImageEntity:
		return &Image{}
	case ImageProxyEntity:
		return &ImageProxy{}
	case InfoSourceEntity:
		return &InfoSource{}
	case InteractiveEntity:
		return &Interactive{}
	case JavaScriptEntity:
		return &JavaScript{}
	case LocationEntity:
		return &LocationTaxonomy{}
	case MapEntity:
		return &Map{}
	case PersonEntity:
		return &Person{}
	case ProgramEntity:
		return &Program{}
	case RecipeEntity:
		return &Recipe{}
	case SeriesEntity:
		return &Series{}
	case SymbolEntity:
		return &Symbol{}
	case TaxonomyEntity:
		return &Taxonomy{}
	case TeaserEntity:
		return &Teaser{}
	case TickerEntity:
		return &Ticker{}
	case TrackEntity:
		return &Track{}
	case VideoEntity:
		return &Video{}
	case VideoEpisodeEntity:
		return &VideoEpisode{}
	case VideoSegmentEntity:
		return &VideoSegment{}
	case ViewTypeEntity:
		return &ViewType{}
	case PollEntity:
		return &Poll{}
	case AnswerItemEntity:
		return &AnswerItem{}
	default:
		return nil
	}
}

// GQLModel returns the mapped Model to use for capi3 GraphQL
func GQLModel(doctype string) Processor {
	switch strings.ToLower(doctype) {
	case AudioSegmentEntity, AudioEpisodeEntity:
		doctype = AudioEntity
	case DynamicCollectionEntity:
		doctype = CollectionEntity
	case ImageProxyEntity:
		doctype = ImageEntity
	case VideoSegmentEntity, VideoEpisodeEntity:
		doctype = VideoEntity
	}

	return Model(doctype)
}
