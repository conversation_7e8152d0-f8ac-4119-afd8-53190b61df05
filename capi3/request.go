package capi3

import (
	"fmt"
	"net/url"
	"strings"

	"github.com/google/go-querystring/query"
	"github.com/spf13/viper"
	"stash.abc-dev.net.au/ter/lib"
	"stash.abc-dev.net.au/ter/lib/cache"
)

const (
	// ActionCreate is the constant name for a create action
	ActionCreate = "create"

	// ActionUpdate is the constant name for an update action
	ActionUpdate = "update"

	// ActionDelete is the constant name for a delete action
	ActionDelete = "delete"

	// ActionHDelete is the constant name for a hard delete action
	ActionHDelete = "hdelete"
)

// CAPI3 request ARN
const (
	CAPI3_CONTENT_REQUEST_ARN    = "CAPI3_CONTENT_REQUEST_ARN"
	CAPI3_SEARCH_REQUEST_ARN     = "CAPI3_SEARCH_REQUEST_ARN"
	CAPI3_TAXONOMY_REQUEST_ARN   = "CAPI3_TAXONOMY_REQUEST_ARN"
	CAPI3_URL_LOOKUP_REQUEST_ARN = "CAPI3_URL_LOOKUP_REQUEST_ARN"
	ContentSourceName            = lib.Capi3
	DEFAULT_LIMIT                = 25
)

// ContentSource returns the uppercase of passing string or default value
func ContentSource(src string) string {
	if src == "" {
		return strings.ToUpper(ContentSourceName)
	}

	return strings.ToUpper(src)
}

// ContentRequest are the required parameters to make a request to CAPI
// Params need only be used in this request for collection type requests.
type ContentRequest struct {
	Action                string `json:"action"`
	ContentType           string `json:"contenttype"`
	ContentID             string `json:"contentid"`
	Params                ContentParams
	Version               int    `json:"contentversion"`
	Source                string `json:"contentsource"`
	Rid                   string `json:"rid"`
	FromNotice            bool   `json:"fromNotice,omitempty"`
	EventSource           string `json:"eventsource,omitempty"`
	ContentValidityStatus string `json:"contentvaliditystatus,omitempty"`
}

// ContentParams is a list of allowable paramaters to a normal content request
type ContentParams struct {
	Limit      int    `json:"limit,omitempty" url:"limit,omitempty"`
	Offset     int    `json:"offset,omitempty" url:"offset,omitempty"`
	ShowEmbeds string `json:"showEmbeds,omitempty" url:"showEmbeds,omitempty"`
	// RequestSource is for distinction the topic request that sends from API to Enrichment
	RequestSource string `json:"requestSource,omitempty" url:"requestSource,omitempty"`
}

// URLValues returns the url values of params
func (p ContentParams) URLValues() (url.Values, error) {
	return query.Values(p)
}

// NewContentRequest constructs a content request for a CAPI document
func NewContentRequest(action lib.Action, contentType, contentID, reqID string, params ContentParams) ContentRequest {
	return ContentRequest{
		Action:      strings.ToLower(action),
		ContentType: strings.ToLower(contentType),
		ContentID:   contentID,
		Source:      strings.ToUpper(ContentSourceName),
		Version:     1,
		Params:      params,
		Rid:         reqID,
	}
}

// CacheKey returns key for caching. format is mapi::lower(entity)::{arid}
func (r ContentRequest) CacheKey() string {
	params, _ := query.Values(r.Params)
	params.Del("showEmbeds")
	return cache.CacheKey(ContentSourceName, []string{r.DocID()}, params)
}

// DocType returns the string lowered view of an objects doctype
func (r ContentRequest) DocType() string {
	return strings.ToLower(r.ContentType)
}

// DocID returns the string lowered view of an objects id
func (r ContentRequest) DocID() string {
	return strings.ToLower(r.ContentID)
}

// URI returns canonical URI base on request info
func (r ContentRequest) URI() string {
	return lib.URI(lib.Coremedia, r.DocType(), r.DocID())
}

// IsFromNotice determines if this message is from an sqs message or not.
func (r ContentRequest) IsFromNotice() bool {
	return r.FromNotice
}

// GetAction returns the action of request
func (r ContentRequest) GetAction() (lib.Action, error) {
	switch strings.ToLower(r.Action) {
	case ActionCreate:
		return lib.ActionCreate, nil
	case ActionUpdate:
		return lib.ActionUpdate, nil
	case ActionDelete:
		return lib.ActionDelete, nil
	case ActionHDelete:
		return lib.ActionHDelete, nil
	default:
		return "", fmt.Errorf("Invalid action: %s", r.Action)
	}
}

// FunctionName returns the config set Lambda function name
func (r ContentRequest) FunctionName() string {
	return viper.GetString(CAPI3_CONTENT_REQUEST_ARN)
}

// ContentSource returns the source that the request is asking
func (r ContentRequest) ContentSource() string {
	return ContentSource(r.Source)
}

// RequestID returns the random 8 character ID generated by Enrichment. It will return "sqs" if generated by notification
func (r ContentRequest) RequestID() string {
	return r.Rid
}

// RequestType returns the type of request: Content, Search, Live, etc.
func (r ContentRequest) RequestType() lib.RequestType {
	return lib.ContentRequest
}

// Path returns url path of the request
func (r ContentRequest) Path() (string, error) {
	path := "/content/id/" + r.DocID()
	params, _ := r.Params.URLValues()
	return lib.JoinPath(path, params.Encode())
}

// SearchRequest allows for a request to be made to Coremedia
type SearchRequest struct {
	Params SearchParams `json:"params,omitempty"`
	ReqID  string
}

// TER-3004 SearchParams contains query params of request
// Doctype, GenreTaxonomy, LocationTaxonomy and SubjectTaxonomy are using string slice is for let graphql consumer know
// those fields support multiple values. Using single string can still support but require extra document to explain usage.
type SearchParams struct {
	FirstPublishedStartDate string   `json:"firstPublishedStartDate,omitempty" url:"firstPublishedStartDate,omitempty"`
	FirstPublishedEndDate   string   `json:"firstPublishedEndDate,omitempty" url:"firstPublishedEndDate,omitempty"`
	LastPublishedStartDate  string   `json:"lastPublishedStartDate,omitempty" url:"lastPublishedStartDate,omitempty"`
	LastPublishedEndDate    string   `json:"lastPublishedEndDate,omitempty" url:"lastPublishedEndDate,omitempty"`
	BroadcastStartDate      string   `json:"broadcastStartDate,omitempty" url:"broadcastStartDate,omitempty"`
	BroadcastEndDate        string   `json:"broadcastEndDate,omitempty" url:"broadcastEndDate,omitempty"`
	AudioType               string   `json:"audioType,omitempty" url:"audioType,omitempty"`
	SeriesID                string   `json:"seriesId,omitempty" url:"seriesId,omitempty"`
	Limit                   int      `json:"limit,omitempty" url:"limit,omitempty"`
	Offset                  int      `json:"offset,omitempty" url:"offset,omitempty"`
	Doctype                 []string `json:"doctype,omitempty" url:"doctype,omitempty,comma"`
	ContributorID           int      `json:"contributorId,omitempty" url:"contributorId,omitempty"`
	ContributorRole         string   `json:"contributorRole,omitempty" url:"contributorRole,omitempty"`
	GenreTaxonomy           []string `json:"genreTaxonomy,omitempty" url:"genreTaxonomy,omitempty,comma"`
	LocationTaxonomy        []string `json:"locationTaxonomy,omitempty" url:"locationTaxonomy,omitempty,comma"`
	SubjectTaxonomy         []string `json:"subjectTaxonomy,omitempty" url:"subjectTaxonomy,omitempty,comma"`
	ThreadTopicTaxonomy     []string `json:"threadTopicTaxonomy,omitempty" url:"threadTopicTaxonomy,omitempty,comma"`
	UseTaxonomyNames        bool     `json:"useTaxonomyNames,omitempty" url:"useTaxonomyNames,omitempty"`
	SortField               string   `json:"sortField,omitempty" url:"sortField,omitempty"`
	SortOrder               string   `json:"sortOrder,omitempty" url:"sortOrder,omitempty"`
	LegacyID                string   `json:"legacyId,omitempty" url:"legacyId,omitempty"`
	LegacyURL               string   `json:"legacyUrl,omitempty" url:"legacyUrl,omitempty"`
	ExternalID              string   `json:"externalId,omitempty" url:"externalId,omitempty"`
	Context                 []string `json:"context,omitempty" url:"context,omitempty,comma"` // TER-3004 To ParseParams url.Values, that only accepts string slice
	Regions                 []string `json:"regions,omitempty" url:"regions,omitempty,comma"`
}

// URLValues returns the url values of params
func (p SearchParams) URLValues() (url.Values, error) {
	return query.Values(p)
}

// NewSearchRequest constructs a search request for CAPI
func NewSearchRequest(entity string, params SearchParams, reqID string) SearchRequest {
	docTypes := make([]string, len(params.Doctype))
	for i, docType := range params.Doctype {
		docTypes[i] = strings.ToLower(docType)
	}
	params.Doctype = docTypes
	return SearchRequest{
		Params: params,
		ReqID:  reqID,
	}
}

// CacheKey returns key for caching. format is capi::search::lower(entity)::{serviceId}
func (r SearchRequest) CacheKey() string {
	params, _ := r.Params.URLValues()
	return cache.CacheKey(ContentSourceName, []string{lib.TypeSearch}, params)
}

// IsFromNotice always returns false as this is a search request - there are no notifications
func (r SearchRequest) IsFromNotice() bool {
	return false
}

// GetAction returns the action of request
func (r SearchRequest) GetAction() (lib.Action, error) {
	return "", nil
}

// FunctionName returns the config set Lambda function name
func (r SearchRequest) FunctionName() string {
	return viper.GetString(CAPI3_SEARCH_REQUEST_ARN)
}

// ContentSource returns the source that the request is asking
func (r SearchRequest) ContentSource() string {
	return ContentSource("")
}

// RequestID returns the random 8 character ID generated by Enrichment.
func (r SearchRequest) RequestID() string {
	return r.ReqID
}

// RequestType returns the type of request: Content, Search, Live, etc.
func (r SearchRequest) RequestType() lib.RequestType {
	return lib.SearchRequest
}

// DocType returns the string lowered search request type name
func (r SearchRequest) DocType() string {
	return lib.TypeSearch
}

// Path returns url path of the request
func (r SearchRequest) Path() (string, error) {
	path := "/content"
	params, _ := r.Params.URLValues()
	return lib.JoinPath(path, params.Encode())
}

// DocID does not return anything here as search requests do not have a unique identifier
func (r SearchRequest) DocID() string {
	return ""
}

type TaxonomyRequest struct {
	Params TaxonomyParams `json:"params,omitempty"`
	ReqID  string
}

type TaxonomyParams struct {
	Limit        int    `json:"limit,omitempty" url:"limit,omitempty"`
	Offset       int    `json:"offset,omitempty" url:"offset,omitempty"`
	Prefix       string `json:"prefix,omitempty" url:"prefix,omitempty"`
	TaxonomyType string `json:"taxonomyType,omitempty" url:"taxonomyType,omitempty"`
	Context      int    `json:"context,omitempty" url:"context,omitempty"`
}

// URLValues returns the url values of params
func (p TaxonomyParams) URLValues() (url.Values, error) {
	return query.Values(p)
}

func NewTaxonomyRequest(params TaxonomyParams, reqID string) TaxonomyRequest {
	return TaxonomyRequest{
		Params: params,
		ReqID:  reqID,
	}
}

// CacheKey returns key for caching. format is capi3::taxonomy::params
func (r TaxonomyRequest) CacheKey() string {
	params, _ := r.Params.URLValues()
	return cache.CacheKey(ContentSourceName, []string{lib.TypeTaxonomy}, params)
}

// DocType returns the string lowered taxonomy request type name
func (r TaxonomyRequest) DocType() string {
	return lib.TypeTaxonomy
}

// FunctionName returns the config set Lambda function name
func (r TaxonomyRequest) FunctionName() string {
	return viper.GetString(CAPI3_TAXONOMY_REQUEST_ARN)
}

// Path returns url path of the request
func (r TaxonomyRequest) Path() (string, error) {
	path := "/taxonomy"
	params, _ := r.Params.URLValues()
	return lib.JoinPath(path, params.Encode())
}

// ContentSource returns the source that the request is asking
func (r TaxonomyRequest) ContentSource() string {
	return ContentSource("")
}

// RequestID returns the random 8 character ID generated by Enrichment.
func (r TaxonomyRequest) RequestID() string {
	return r.ReqID
}

// RequestType returns the type of request: Content, Search, Live, etc.
func (r TaxonomyRequest) RequestType() lib.RequestType {
	return lib.TaxonomyRequest
}

// DocID does not return anything here as taxonomy requests do not have a unique identifier
func (r TaxonomyRequest) DocID() string {
	return ""
}

// IsFromNotice always returns false as this is a taxonomy request - there are no notifications
func (r TaxonomyRequest) IsFromNotice() bool {
	return false
}

// GetAction returns the action of request
func (r TaxonomyRequest) GetAction() (lib.Action, error) {
	return "", nil
}

type URLLookupRequest struct {
	Params URLLookupParams `json:"params,omitempty"`
	ReqID  string
}

type URLLookupParams struct {
	Path string `json:"path,omitempty" url:"path,omitempty"`
}

func NewUrlLookupRequest(params URLLookupParams, reqID string) URLLookupRequest {
	return URLLookupRequest{
		Params: params,
		ReqID:  reqID,
	}
}

// URLValues returns the url values of params
func (p URLLookupParams) URLValues() (url.Values, error) {
	return query.Values(p)
}

// CacheKey returns key for caching. format is capi3::urllookup::params
func (r URLLookupRequest) CacheKey() string {
	params, _ := r.Params.URLValues()
	return cache.CacheKey(ContentSourceName, []string{lib.TypeURLLookup}, params)
}

// DocType returns the string lowered url lookup request type name
func (r URLLookupRequest) DocType() string {
	return lib.TypeURLLookup
}

// FunctionName returns the config set Lambda function name
func (r URLLookupRequest) FunctionName() string {
	return viper.GetString(CAPI3_URL_LOOKUP_REQUEST_ARN)
}

// Path returns url path of the request
func (r URLLookupRequest) Path() (string, error) {
	path := "/content/url-path/"
	params, _ := r.Params.URLValues()
	return lib.JoinPath(path, params.Encode())
}

// ContentSource returns the source that the request is asking
func (r URLLookupRequest) ContentSource() string {
	return ContentSource("")
}

// RequestID returns the random 8 character ID generated by Enrichment.
func (r URLLookupRequest) RequestID() string {
	return r.ReqID
}

// RequestType returns the type of request: Content, Search, Live, etc.
func (r URLLookupRequest) RequestType() lib.RequestType {
	return lib.URLLookupRequest
}

// DocID does not return anything here as url lookup requests do not have a unique identifier
func (r URLLookupRequest) DocID() string {
	return ""
}

// IsFromNotice always returns false as this is a url lookup request - there are no notifications
func (r URLLookupRequest) IsFromNotice() bool {
	return false
}

// GetAction returns the action of request
func (r URLLookupRequest) GetAction() (lib.Action, error) {
	return "", nil
}
