package capi3

// CMSymbol is a top level CMSymbol document returned from CAPI
type CMSymbol struct {
	CMObject

	Description string `json:"description,omitempty"`
	Name        string `json:"name,omitempty"`
}

// Process converts data to Terminus required formats
func (c *CMSymbol) Process() {}
func (c *CMSymbol) IsCapi()  {}

//Don't think there are situations where Symbols are not valid?
func (c *CMSymbol) Valid() ValidStatus {
	return Valid
}
