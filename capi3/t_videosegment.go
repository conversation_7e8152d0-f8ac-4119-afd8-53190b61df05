package capi3

// VideoSegment is a top level VideoSegment document returned from CAPI
type VideoSegment struct {
	CMVideo

	ReferringEpisodedID *int            `json:"referringEpisodeId,omitempty"`
	SupportingInfo      *RichTextObject `json:"supportingInfo,omitempty"`
}

// Process converts data to Terminus required formats
func (c *VideoSegment) Process() {
	c.CMVideo.Process()
	c.SupportingInfo.ToFMLWithMeta(c.Meta())
}

func (VideoSegment) IsCapi() {}
