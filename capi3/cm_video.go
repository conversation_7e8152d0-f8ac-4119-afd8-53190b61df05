package capi3

// CMVideo is a top level ABCVideo document returned from CAPI
type CMVideo struct {
	CMMedia

	CaptionURL    string          `json:"captionUrl,omitempty"`
	ClassifAdvice string          `json:"classifAdvice,omitempty"`
	Summary       *RichTextObject `json:"summary,omitempty"`
}

// Process converts data to Terminus required formats
func (c *CMVideo) Process() {
	c.CMMedia.Process()
	c.Summary.ToFMLWithMeta(c.Meta())
}
