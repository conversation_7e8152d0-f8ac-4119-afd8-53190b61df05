package capi3

import "strings"

// CMTeasable contains content shared by all CMTeasable Capi documents
type CMTeasable struct {
	CMLinkable

	Byline             *RichTextObject `json:"byline,omitempty"`
	EditorialNotes     *RichTextObject `json:"editorialNotes,omitempty"`
	ExternalID         string          `json:"externalId,omitempty"`
	FirstPublishedDate string          `json:"firstPublishedDate,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	Importance         *int            `json:"importance,omitempty"`
	IsNotChildFriendly *bool           `json:"isNotChildFriendly,omitempty"`
	LastPublishedDate  string          `json:"lastPublishedDate,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	Latitude           string          `json:"latitude,omitempty"`
	LegacyID           string          `json:"legacyID,omitempty"`
	LegacyURL          string          `json:"legacyURL,omitempty"`
	LinkText           string          `json:"linkText,omitempty"`
	Longitude          string          `json:"longitude,omitempty"`
	PublicationDate    string          `json:"publicationDate,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	ShortTeaserText    *RichTextObject `json:"shortTeaserText,omitempty"`
	ShortTeaserTitle   string          `json:"shortTeaserTitle,omitempty"`
	SortTitle          string          `json:"sortTitle,omitempty"`
	SourceSystem       string          `json:"sourceSystem,omitempty"`
	TeaserText         *RichTextObject `json:"teaserText,omitempty"`
	TeaserTitle        string          `json:"teaserTitle,omitempty"`
	URLSegment         string          `json:"urlSegment,omitempty"`
}

// Process converts data to Terminus required formats
func (t *CMTeasable) Process() {
	t.CMLinkable.Process()
	t.Byline.ToFMLWithMeta(t.Meta())
	t.EditorialNotes.ToFMLWithMeta(t.Meta())
	t.ShortTeaserText.ToFMLWithMeta(t.Meta())
	t.TeaserText.ToFMLWithMeta(t.Meta())
}

func (t *CMTeasable) BylinePlain() string {
	if t.Byline == nil {
		return ""
	}

	return t.Byline.PlainText
}

func (t *CMTeasable) BylineFML() (string, error) {
	if t.Byline == nil {
		return "", nil
	}

	return string(t.Byline.JSON), nil
}

func (t *CMTeasable) BylineMarkup() string {
	if t.Byline == nil {
		return ""
	}

	return t.Byline.Markup
}

func (t *CMTeasable) Teasable() *CMTeasable {
	return t
}

func (t *CMTeasable) NotChildFriendly() bool {
	return *t.IsNotChildFriendly
}

func (t *CMTeasable) FirstUpdated() string {
	return t.FirstPublishedDate
}

func (t *CMTeasable) LastUpdated() string {
	return t.LastPublishedDate
}

type Teasable interface {
	Teasable() *CMTeasable
}

// ParseExternalID parses the external ID into upstream and id
func ParseExternalID(eid string) (string, string) {
	if eid == "" {
		return "", ""
	}
	s := strings.Split(eid, ":")
	if len(s) == 1 {
		return "", ""
	}
	return s[0], s[1]
}
