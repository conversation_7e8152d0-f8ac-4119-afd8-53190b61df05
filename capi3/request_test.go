package capi3

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSearchRequestPath(t *testing.T) {
	params := SearchParams{
		Doctype:          []string{ArticleEntity, VideoEntity},
		GenreTaxonomy:    []string{"Xbox 360", "Cats"},
		LocationTaxonomy: []string{"6998", "6992"},
		SubjectTaxonomy:  []string{"Event", "Environment"},
	}
	req := NewSearchRequest("", params, "req")
	path, err := req.Path()
	assert.Nil(t, err)
	assert.Equal(t, "/content?doctype=article%2Cvideo&genreTaxonomy=Xbox+360%2CCats&locationTaxonomy=6998%2C6992&subjectTaxonomy=Event%2CEnvironment", path)
}
