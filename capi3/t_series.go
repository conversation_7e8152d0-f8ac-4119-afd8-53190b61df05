package capi3

// Series is a top level ABCAudio document returned from CAPI
type Series struct {
	CMCollection

	EndDate        string          `json:"endDate,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	SeriesNumber   *int            `json:"seriesNumber,omitempty"`
	StartDate      string          `json:"startDate,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	SupportingInfo *RichTextObject `json:"supportingInfo,omitempty"`
	Text           *RichTextObject `json:"text,omitempty"`
	Total          *int            `json:"total,omitempty"`
}

// Process converts data to Terminus required formats
func (a *Series) Process() {
	a.CMCollection.Process()
	a.SupportingInfo.ToFMLWithMeta(a.Meta())
	a.Text.ToFMLWithMeta(a.Meta())
}

func (Series) IsCapi() {}
