package capi3

// Feed contains Feed specific document fields https://confluence.abc-dev.net.au/display/CMUPGRADE/Capi+Mapping+Rules+for+Feed+document
type Feed struct {
	CMTeasable
	Description        string    `json:"description"`
	DescriptionShort   string    `json:"descriptionShort"`
	Author             string    `json:"author"`
	Copyright          string    `json:"copyright"`
	Email              string    `json:"email"`
	ItunesExplicit     string    `json:"itunesExplicit"`
	RSSDocs            string    `json:"rssDocs"`
	RSSCategory        string    `json:"rssCategory"`
}

// Process converts data to Terminus required formats
func (a *Feed) Process() {
	a.CMTeasable.Process()
}

func (Feed) IsCapi() {}
