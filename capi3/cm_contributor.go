package capi3

// CMContributor is a top level ABCContributor document returned from CAPI
type CMContributor struct {
	CMTeasable

	ABCStaff        *bool  `json:"abcStaff,omitempty"`
	Contactable     *bool  `json:"contactable,omitempty"`
	ContributorName string `json:"contributorName,omitempty"`
	Email           string `json:"email,omitempty"`
}

// Process converts data to Terminus required formats
func (c *CMContributor) Process() {
	c.CMTeasable.Process()
}
