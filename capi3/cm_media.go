package capi3

// CMMedia is a top level CmMedia document returned from CAPI
type CMMedia struct {
	CMTeasable

	Caption     *RichTextObject `json:"caption,omitempty"`
	Duration    *int            `json:"duration,omitempty"`
	GeoBlocking *bool           `json:"geoBlocking,omitempty"`
	MediaExpiry string          `json:"mediaExpiry,omitempty" terminus_rest:"date_reformat(capi,rest)" terminus_gql:"date_reformat(capi,std)"`
	Renditions  []*Rendition    `json:"renditions,omitempty"`
	ResourceID  string          `json:"resourceId,omitempty"`
	Transcript  *RichTextObject `json:"transcript,omitempty"`
}

// Process converts data to Terminus required formats
func (c *CMMedia) Process() {
	c.CMTeasable.Process()
	c.Caption.ToFMLWithMeta(c.Meta())
	c.Transcript.ToFMLWithMeta(c.Meta())
}

// Media returns CMMedia itself for MultiMedia interface
func (c *CMMedia) Media() *CMMedia {
	return c
}
