package capi3

// Track is a top level Track document returned from CAPI
type Track struct {
	CMEntity

	Album           string `json:"album,omitempty"`
	Artists         string `json:"artists,omitempty"`
	Composers       string `json:"composers,omitempty"`
	MusicBrainzGUID string `json:"musicBrainzGuid,omitempty"`
}

// Process converts data to Terminus required formats
func (c *Track) Process() {
	c.CMEntity.Process()
}

func (Track) IsCapi() {}
