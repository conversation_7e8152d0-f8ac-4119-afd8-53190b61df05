package capi3

// Person is a top level Person document returned from CAPI
type Person struct {
	CMContributor

	Biography *RichTextObject `json:"biography,omitempty"`
	FirstName string          `json:"firstName,omitempty"`
	LastName  string          `json:"lastName,omitempty"`
	Title     string          `json:"title,omitempty"`
}

// Process converts data to Terminus required formats
func (c *Person) Process() {
	c.CMContributor.Process()
	c.Biography.ToFMLWithMeta(c.Meta())
}

func (Person) IsCapi() {}
