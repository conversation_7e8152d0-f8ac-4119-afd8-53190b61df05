package capi3

// Video is a top level Video document returned from CAPI
type Video struct {
	CMVideo

	// Support VideoEpisode as Video in GraphQL
	EpisodeNumber *int `json:"episodeNumber,omitempty"`

	// Support VideoSegment as Video in GraphQL
	ReferringEpisodedID *int `json:"referringEpisodeId,omitempty"`

	SupportingInfo *RichTextObject `json:"supportingInfo,omitempty"`
}

// Process converts data to Terminus required formats
func (c *Video) Process() {
	c.CMVideo.Process()
	c.SupportingInfo.ToFMLWithMeta(c.Meta())
}

func (Video) IsCapi() {}
