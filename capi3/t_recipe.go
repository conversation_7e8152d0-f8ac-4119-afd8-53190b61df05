package capi3

// Recipe is a top level ABCAudio document returned from CAPI
type Recipe struct {
	CMTeasable

	CookingTime     *int            `json:"cookingTime,omitempty"`
	Ingredients     *RichTextObject `json:"ingredients,omitempty"`
	Instructions    *RichTextObject `json:"instructions,omitempty"`
	PreparationTime *int            `json:"preparationTime,omitempty"`
	Text            *RichTextObject `json:"text,omitempty"`
	TotalTime       *int            `json:"totalTime,omitempty"`
	Yield           string          `json:"yield,omitempty"`
}

// Process converts data to Terminus required formats
func (a *Recipe) Process() {
	a.CMTeasable.Process()
	a.Instructions.ToFMLWithMeta(a.Meta())
	a.Ingredients.ToFMLWithMeta(a.Meta())
	a.Text.ToFMLWithMeta(a.Meta())
}

func (Recipe) IsCapi() {}
