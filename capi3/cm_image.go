package capi3

// CMImage contains image specific document fields
type CMImage struct {
	CMTeasable

	Alt              string          `json:"alt,omitempty"`
	Caption          *RichTextObject `json:"caption,omitempty"`
	DefaultRatio     string          `json:"defaultRatio,omitempty"`
	Height           *int            `json:"height,omitempty"`
	OriginalEnforced *bool           `json:"isOriginalEnforced,omitempty"`
	ResourceID       string          `json:"resourceId,omitempty"`
	URL              string          `json:"url,omitempty"`
	Width            *int            `json:"width,omitempty"`
}

// Process converts data to Terminus required formats
func (a *CMImage) Process() {
	a.CMTeasable.Process()
	a.Caption.ToFMLWithMeta(a.Meta())
}

func (a *CMImage) IsOriginalEnforced() *bool {
	return a.OriginalEnforced
}
