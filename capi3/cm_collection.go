package capi3

// CMCollection contains content shared by all Collection documents
type CMCollection struct {
	CMTeasable

	EditorialLimit *int        `json:"editorialLimit,omitempty"`
	MaxElements    *int        `json:"maxElements,omitempty"`
	Pagination     *Pagination `json:"pagination,omitempty"`
}

// Process converts data to Terminus required formats
func (l *CMCollection) Process() {
	l.CMTeasable.Process()
}
