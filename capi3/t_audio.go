package capi3

// Audio contains Audio specific document fields
type Audio struct {
	CMMedia

	// Support AudioEpisode as Audio in GraphQL
	EpisodeNumber *int `json:"episodeNumber,omitempty"`

	SupportingInfo *RichTextObject `json:"supportingInfo,omitempty"`
}

// Process converts data to Terminus required formats
func (a *Audio) Process() {
	a.CMMedia.Process()
	a.SupportingInfo.ToFMLWithMeta(a.Meta())
}

func (Audio) IsCapi() {}
