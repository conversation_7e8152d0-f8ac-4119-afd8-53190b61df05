package lib

import (
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

type EmbedParams struct {
	FieldEmbedString string `json:"embedString,omitempty" url:"embedString,omitempty"`
	FieldEmbedInt    int    `json:"embedInt,omitempty" url:"embedInt,omitempty"`
}

type TestParam struct {
	FieldStr      string `json:"strj,omitempty" url:"str,omitempty"`
	FieldJSON     string `json:"strjson,omitempty"`
	FieldBare     string
	FieldIgnore   string   `url:"-"`
	FieldInt      int      `url:"int,omitempty"`
	FieldInt8     int8     `url:"int8,omitempty"`
	FieldInt16    int16    `url:"int16,omitempty"`
	FieldInt32    int32    `url:"int32,omitempty"`
	FieldInt64    int64    `url:"int64,omitempty"`
	FieldUint     uint     `url:"uint,omitempty"`
	FieldUint8    uint8    `url:"uint8,omitempty"`
	FieldUint16   uint16   `url:"uint16,omitempty"`
	FieldUint32   uint32   `url:"uint32,omitempty"`
	FieldUint64   uint64   `url:"uint64,omitempty"`
	FieldBool     bool     `url:"bool,omitempty"`
	FieldSlice    []string `url:"slice,comma,omitempty"`
	FieldSlice1   []string `url:"slice1,omitempty"`
	FieldIntSlice []int    `url:"intSlice,comma,omitempty"`
	EmbedParams
}

func TestParseParamsSetCorrectValue(t *testing.T) {
	params := make(url.Values)
	params.Set("str", "string field")
	params.Set("strjson", "string json field")
	params.Set("FieldBare", "string bare field")
	params.Set("FieldIgnore", "ignore field")
	params.Set("int", "-1")
	params.Set("int8", "-8")
	params.Set("int16", "-16")
	params.Set("int32", "-32")
	params.Set("int64", "-64")
	params.Set("uint", "1")
	params.Set("uint8", "8")
	params.Set("uint16", "16")
	params.Set("uint32", "32")
	params.Set("uint64", "64")
	params.Set("bool", "true")
	params.Set("embedString", "embed")
	params.Set("embedInt", "-11")
	params.Set("slice", "a1,a2,a3")
	params["slice1"] = []string{"a1", "a2", "a3"}

	s := &TestParam{}
	err := ParseParams(params, s)
	assert.NoError(t, err)

	assert.Equal(t, s.FieldStr, "string field")
	assert.Equal(t, s.FieldJSON, "string json field")
	assert.Equal(t, s.FieldBare, "string bare field")
	assert.Zero(t, s.FieldIgnore)
	assert.Equal(t, s.FieldInt, -1)
	assert.Equal(t, s.FieldInt8, int8(-8))
	assert.Equal(t, s.FieldInt16, int16(-16))
	assert.Equal(t, s.FieldInt32, int32(-32))
	assert.Equal(t, s.FieldInt64, int64(-64))
	assert.Equal(t, s.FieldUint, uint(1))
	assert.Equal(t, s.FieldUint8, uint8(8))
	assert.Equal(t, s.FieldUint16, uint16(16))
	assert.Equal(t, s.FieldUint32, uint32(32))
	assert.Equal(t, s.FieldUint64, uint64(64))
	assert.True(t, s.FieldBool)
	assert.Equal(t, s.FieldSlice, []string{"a1", "a2", "a3"})
	assert.Equal(t, s.FieldSlice1, []string{"a1", "a2", "a3"})
	assert.Equal(t, s.EmbedParams.FieldEmbedString, "embed")
	assert.Equal(t, s.EmbedParams.FieldEmbedInt, -11)
}

func TestParseParamsParseIntError(t *testing.T) {
	params := make(url.Values)
	params.Set("int", "a")

	s := &TestParam{}
	err := ParseParams(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query int with value a")
}

func TestParseParamsParseInt8Error(t *testing.T) {
	params := make(url.Values)
	params.Set("int8", "a")

	s := &TestParam{}
	err := ParseParams(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query int8 with value a")
}

func TestParseParamsParseUintError(t *testing.T) {
	params := make(url.Values)
	params.Set("uint", "a")

	s := &TestParam{}
	err := ParseParams(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query uint with value a")
}

func TestParseParamsParseBoolError(t *testing.T) {
	params := make(url.Values)
	params.Set("bool", "a")

	s := &TestParam{}
	err := ParseParams(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query bool with value a")
}

func TestParseParamsStrictSetCorrectValue(t *testing.T) {
	params := make(url.Values)
	params.Set("str", "string field")
	params.Set("strjson", "string json field")
	params.Set("FieldBare", "string bare field")
	params.Set("int", "-1")
	params.Set("int8", "-8")
	params.Set("int16", "-16")
	params.Set("int32", "-32")
	params.Set("int64", "-64")
	params.Set("uint", "1")
	params.Set("uint8", "8")
	params.Set("uint16", "16")
	params.Set("uint32", "32")
	params.Set("uint64", "64")
	params.Set("bool", "true")
	params.Set("embedString", "embed")
	params.Set("embedInt", "-11")

	s := &TestParam{}
	err := ParseParamsStrict(params, s)
	assert.NoError(t, err)

	assert.Equal(t, s.FieldStr, "string field")
	assert.Equal(t, s.FieldJSON, "string json field")
	assert.Equal(t, s.FieldBare, "string bare field")
	assert.Zero(t, s.FieldIgnore)
	assert.Equal(t, s.FieldInt, -1)
	assert.Equal(t, s.FieldInt8, int8(-8))
	assert.Equal(t, s.FieldInt16, int16(-16))
	assert.Equal(t, s.FieldInt32, int32(-32))
	assert.Equal(t, s.FieldInt64, int64(-64))
	assert.Equal(t, s.FieldUint, uint(1))
	assert.Equal(t, s.FieldUint8, uint8(8))
	assert.Equal(t, s.FieldUint16, uint16(16))
	assert.Equal(t, s.FieldUint32, uint32(32))
	assert.Equal(t, s.FieldUint64, uint64(64))
	assert.True(t, s.FieldBool)
	assert.Equal(t, s.EmbedParams.FieldEmbedString, "embed")
	assert.Equal(t, s.EmbedParams.FieldEmbedInt, -11)
}

func TestParseParamsStrictErrorByFieldIgnore(t *testing.T) {
	params := make(url.Values)
	params.Set("str", "string field")
	params.Set("strjson", "string json field")
	params.Set("FieldBare", "string bare field")
	params.Set("FieldIgnore", "ignore field")
	params.Set("int", "-1")
	params.Set("int8", "-8")
	params.Set("int16", "-16")
	params.Set("int32", "-32")
	params.Set("int64", "-64")
	params.Set("uint", "1")
	params.Set("uint8", "8")
	params.Set("uint16", "16")
	params.Set("uint32", "32")
	params.Set("uint64", "64")
	params.Set("bool", "true")

	s := &TestParam{}
	err := ParseParamsStrict(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Unsupported param FieldIgnore")
}

func TestParseParamsStrictErrorParseInt(t *testing.T) {
	params := make(url.Values)
	params.Set("int", "a")

	s := &TestParam{}
	err := ParseParamsStrict(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query int with value a")
}

func TestParseParamsStrictErrorParseInt8(t *testing.T) {
	params := make(url.Values)
	params.Set("int8", "a")

	s := &TestParam{}
	err := ParseParamsStrict(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query int8 with value a")
}

func TestParseParamsStrictErrorParseUint(t *testing.T) {
	params := make(url.Values)
	params.Set("uint", "a")

	s := &TestParam{}
	err := ParseParamsStrict(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query uint with value a")
}

func TestParseParamsStrictErrorParseBool(t *testing.T) {
	params := make(url.Values)
	params.Set("bool", "a")

	s := &TestParam{}
	err := ParseParamsStrict(params, s)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Error on converting query bool with value a")
}
