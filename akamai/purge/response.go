package purge

import "fmt"

type Response struct {
	HTTPStatus       int    `json:"httpStatus,omitempty"`
	Detail           string `json:"detail,omitempty"`
	SupportID        string `json:"supportId,omitempty"`
	PurgeID          string `json:"purgeId,omitempty"`
	EstimatedSeconds int    `json:"estimatedSeconds,omitempty"`
}

type ErrorResponse struct {
	Type        string `json:"type,omitempty"`
	Title       string `json:"title,omitempty"`
	Status      int64  `json:"status,omitempty"`
	Detail      string `json:"detail,omitempty"`
	Instance    string `json:"instance,omitempty"`
	Method      string `json:"method,omitempty"`
	ServerIP    string `json:"serverIp,omitempty"`
	ClientIP    string `json:"clientIp,omitempty"`
	RequestID   string `json:"requestId,omitempty"`
	RequestTime string `json:"requestTime,omitempty"`
}

func (e *ErrorResponse) Error() string {
	return fmt.Sprintf("%s: %s", e.Title, e.Detail)
}
