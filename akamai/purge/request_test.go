package purge

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPurgeDelete(t *testing.T) {
	objects := []string{"test", "h100"}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintln(w, "Hello, client")
	}))
	defer ts.Close()

	cfg := Config{
		Network: NetworkStaging,
	}

	u, _ := url.Parse(ts.URL)
	t.Setenv("AKAMAI_HOST", u.Host)
	t.Setenv("AKAMAI_CLIENT_TOKEN", "1234")
	t.Setenv("AKAMAI_CLIENT_SECRET", "5678")
	t.Setenv("AKAMAI_ACCESS_TOKEN", "abcd")

	req, err := NewRequest(cfg, Host(ts.URL))
	assert.NoError(t, err)
	_, err = req.Delete(context.Background(), PurgeByCacheTag, objects)
	assert.NoError(t, err)
}
