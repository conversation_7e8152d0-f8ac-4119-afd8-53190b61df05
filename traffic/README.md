# Package for traffic control

## Background

Terminus need to handle big traffic and it also need to control the traffic to upstream services. This package will contains all the parts doing traffic handling/control.

## Traffic Controller

Traffic controller is a general interface for control traffic when doing upstream calls. It has a simple interface in controller.go:

```go
type Controller interface {
    Request(ctx context.Context, req lib.Request, upstream, fallback DoRequest) (context.Context, []byte, error)
}
```

The usage will be like:

```go
capi3Controller := NewCAPI3Controller() // each upstream need it's own controller for different settings
req := NewRequest()
newCtx, data, err := capi3Controller.Request(ctx, req, func(ctx context.Context, req lib.Request) (context.Context, []byte, error){
        ctx = appendCtx(ctx, key, value) // optional if want to add info to ctx
        resp, err := client.Do(req)
        if err != nil {
            return ctx, nil, MarkNeedsFallback(err)
        }
        if resp.StatusCode >= 500 {
            return ctx, nil, MarkNeedsFallback(err)
        } else if resp.StatusCode > 200 {
            return ctx, nil, err // errors does not need fallback
        }
        return ctx, read(resp), nil
    },func(ctx context.Context, req lib.Request) (context.Context, []byte, error){
        ctx = appendCtx(ctx, key, value) // optional if want to add info to ctx
        resp, err := serveStale.Do(req)
        if err != nil {
            return ctx, nil, MarkNeedsFallback(err)
        }
        return ctx, read(resp), nil
    })
if newCtx.Value(traffic.CtxBlocked) {
    // data is not real upstream data
}
```

## Circuit Breaker

Circuit Breaker is an implementation of traffic.Controller. It applied circuit breaker pattern on high error rate to prevent resource leak.
For more details please read in [Confluence](<https://confluence.abc-dev.net.au/display/TER/Circuit+Breaker>)

## Counter

Counter is a interface of counting success and failure requests. It's complex in high concurrent environment. For details check [here](https://confluence.abc-dev.net.au/display/TER/Circuit+Breaker#CircuitBreaker-Designofthecounter)
