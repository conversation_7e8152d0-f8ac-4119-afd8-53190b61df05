package traffic

import (
	"context"
	"math/rand"
	"testing"
	"time"
)

func BenchmarkSingleIntervalRecord(b *testing.B) {
	bkt := newInterval(100 * time.Millisecond)
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			evt := EventType(rand.Int() % 2)
			now := time.Now()
			bkt.Record(evt, now, 1)
		}
	})
}

func BenchmarkRollingIntervalCounterRecord(b *testing.B) {
	c := NewRollingIntervalCounter(10, 100*time.Millisecond)
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			evt := EventType(rand.Int() % 2)
			c.ReceiveEvent(context.Background(), evt, 1)
		}
	})
}

func BenchmarkRollingIntervalCounterReadInWindow(b *testing.B) {
	var numIntervals int64 = 6000
	var intervalSize time.Duration = 100 * time.Millisecond
	c := NewRollingIntervalCounter(numIntervals, intervalSize)
	ctx := context.Background()
	t := time.Now().UnixNano() - numIntervals*int64(intervalSize)
	c.startTime = t
	for i := int64(0); i < numIntervals; i++ {
		tm := time.Unix(0, t)
		index := c.intervalIndex(tm)
		c.intervals[index].Record(EventFetchSuccess, tm, 1)
		c.intervals[index].Record(EventFetchFailure, tm, 1)
		t += int64(intervalSize)
	}
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			evt := EventType(rand.Int() % 2)
			c.ReceiveEvent(ctx, evt, 1)
			_, err := c.GetValueInWindow(ctx, EventFetchSuccess, 120*time.Second)
			if err != nil {
				b.Error(err)
			}
		}
	})
}

func BenchmarkRollingIntervalCounterRead(b *testing.B) {
	c := NewRollingIntervalCounter(1800, time.Second)
	ctx := context.Background()
	t := time.Now().UnixNano() - int64(1800*time.Second)
	c.startTime = t
	var total int64 = 500
	for i := int64(0); i < total; i++ {
		tm := time.Unix(0, t)
		index := c.intervalIndex(tm)
		c.intervals[index].Record(EventFetchSuccess, tm, 1)
		c.intervals[index].Record(EventFetchFailure, tm, 1)
		t += 18000 / total * int64(time.Second)
	}
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// evt := EventType(rand.Int() % 2)
			// c.ReceiveEvent(ctx, evt, 1)
			c.GetValues(ctx, 1000, EventFetchSuccess, EventFetchFailure)
			// fmt.Println(cnt, duration)
		}
	})
}

func TestRollingIntervalCounter(t *testing.T) {
	var intervalCnt int64 = 10
	intervalSize := 100 * time.Millisecond
	window := time.Duration(intervalCnt) * intervalSize
	c := NewRollingIntervalCounter(intervalCnt, intervalSize)
	successCount := 1453
	failureCount := 325
	receiveCh := make(chan bool)
	go func() {
		for i := 0; i < successCount; i++ {
			time.Sleep(2 * time.Millisecond)
			go func() {
				c.ReceiveEvent(context.Background(), EventFetchSuccess, 1)
				receiveCh <- true
			}()
		}
	}()
	go func() {
		for i := 0; i < failureCount; i++ {
			time.Sleep(5 * time.Millisecond)
			go func() {
				c.ReceiveEvent(context.Background(), EventFetchFailure, 1)
				receiveCh <- false
			}()
		}
	}()
	getCh := time.Tick(window)
	var totalSuccess int64 = 0
	var totalFailure int64 = 0
	countDownSuccess := successCount
	countDownFailure := failureCount
	looping := true
	finished := false
	for looping {
		select {
		case v := <-receiveCh:
			if v {
				countDownSuccess--
			} else {
				countDownFailure--
			}
			if countDownSuccess == 0 && countDownFailure == 0 {
				close(receiveCh)
				finished = true
			}
		case <-getCh:
			successCnt, failureCnt := getResults(t, c, window)
			totalSuccess += successCnt
			totalFailure += failureCnt
			if finished {
				looping = false
			}
		}
	}
	if totalSuccess != int64(successCount) {
		t.Errorf("Incorrect totalSuccess: %d", totalSuccess)
	}
	if totalFailure != int64(failureCount) {
		t.Errorf("Incorrect totalFailure: %d", totalFailure)
	}
}

func getResults(t *testing.T, c *RollingIntervalCounter, window time.Duration) (int64, int64) {
	successCnt, err := c.GetValueInWindow(context.Background(), EventFetchSuccess, window)
	if err != nil {
		t.Error(err)
		t.FailNow()
	}
	t.Logf("%v, EventFetchSuccess: %d", time.Now(), successCnt)
	failureCnt, err := c.GetValueInWindow(context.Background(), EventFetchFailure, window)
	if err != nil {
		t.Error(err)
		t.FailNow()
	}
	t.Logf("%v, EventFetchFailure: %d", time.Now(), failureCnt)
	return successCnt, failureCnt
}

func TestRollingIntervalCounterGetValues(t *testing.T) {
	var intervalCnt int64 = 150
	intervalSize := 100 * time.Millisecond
	c := NewRollingIntervalCounter(intervalCnt, intervalSize)
	receiveCh := make(chan bool)
	getCh := time.Tick(100 * time.Millisecond)
	var totalSuccess int64 = 0
	var totalFailure int64 = 0
	countDownSuccess := 4000
	countDownFailure := 1000
	looping := true
	finished := false
	go func() {
		for i := 0; i < 1000; i++ {
			go func() {
				c.ReceiveEvent(context.Background(), EventFetchSuccess, 4)
				receiveCh <- true
			}()
			time.Sleep(5 * time.Millisecond)
		}
	}()
	go func() {
		for i := 0; i < 1000; i++ {
			go func() {
				c.ReceiveEvent(context.Background(), EventFetchFailure, 1)
				receiveCh <- false
			}()
			time.Sleep(5 * time.Millisecond)
		}
	}()
	for looping {
		select {
		case v := <-receiveCh:
			if v {
				countDownSuccess -= 4
			} else {
				countDownFailure--
			}
			if countDownSuccess == 0 && countDownFailure == 0 {
				close(receiveCh)
				finished = true
			}
		case <-getCh:
			successCnt, failureCnt, _ := getValues(t, c)
			// if successCnt+failureCnt < 1000 {
			// 	t.Errorf("Incorrect successCnt: %d and failureCnt: %d in duration: %v", successCnt, failureCnt, duration)
			// }
			totalSuccess += successCnt
			totalFailure += failureCnt
			if finished {
				looping = false
			}
		}
	}
	t.Log(totalSuccess, totalFailure)
}

func getValues(t *testing.T, c *RollingIntervalCounter) (int64, int64, time.Duration) {
	cnts, duration := c.GetValues(context.Background(), 1000, EventFetchSuccess, EventFetchFailure)
	t.Logf("%v, EventFetchSuccess: %d, EventFetchFailure: %d, duration: %v", time.Now(), cnts[0], cnts[1], duration)
	return cnts[0], cnts[1], duration
}
