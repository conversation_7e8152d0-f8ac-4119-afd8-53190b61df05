package logging

import (
	"sync"
	"sync/atomic"
	"testing"
)

func TestShouldLog(t *testing.T) {
	sampleRate := 10
	count := 100000
	trueCount := atomic.Int32{}
	wg := sync.WaitGroup{}
	for i := 0; i < count; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if ShouldLog(sampleRate) {
				trueCount.Add(1)
			}
		}()
	}
	wg.Wait()
	t.Logf("True count: %d, percentage: %d", trueCount.Load(), int(trueCount.Load())*100/count)
}

func BenchmarkShouldLog(b *testing.B) {
	for i := 0; i < b.N; i++ {
		ShouldLog(10)
	}
}
