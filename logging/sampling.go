package logging

import (
	"math/rand/v2"
	"sync"
	"time"
)

var r = rand.New(rand.NewPCG(uint64(time.Now().Unix()), uint64(time.Now().UnixNano())))
var mutex = sync.Mutex{}

// ShouldLog takes a fixed integer between 1 and 100 to determine the probability of when logging should occur
func ShouldLog(sampleRate int) bool {
	if sampleRate > 99 {
		return true
	}

	if sampleRate < 1 {
		return false
	}

	mutex.Lock()
	defer mutex.Unlock()
	random := r.IntN(99) + 1 //min := 1; max := 100; fmt.Println(rand.Intn(max - min) + min)
	return random <= sampleRate
}
