package datalist

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDoctypesThatHaveParams(t *testing.T) {
	valid := PoorNotifyDoctype("capi3", "dYnamicCollection")
	assert.True(t, valid, "Should return true when checking for content on the list")

	valid = PoorNotifyDoctype("capi3", "Article")
	assert.False(t, valid, "Should return false when checking for doctypes not on the list")

	valid = PoorNotifyDoctype("capi3", "xxx")
	assert.False(t, valid, "Should return false when checking for invalid doctypes")
}
