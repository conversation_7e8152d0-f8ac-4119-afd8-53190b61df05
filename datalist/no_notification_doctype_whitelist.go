package datalist

import "strings"

//unreliableDoctypes is the list of doctypes do not have reliable notifications
var capi3NoNotificationDoctypes = map[string]bool{
	"dynamiccollection": true,
}

//PoorNotifyDoctype indicates if the particular doctype does not have reliable notifications, such as Dynamic Collections.
func PoorNotifyDoctype(upstream, doctype string) bool {
	switch upstream {
	case "capi3":
		if val, ok := capi3NoNotificationDoctypes[strings.ToLower(doctype)]; ok {
			return val
		}
	default:
		return false
	}

	return false
}
