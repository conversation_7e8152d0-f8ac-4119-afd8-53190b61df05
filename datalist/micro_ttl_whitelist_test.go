package datalist

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWhitelist(t *testing.T) {
	valid := IsMicroTTLContent("12412196")
	assert.True(t, valid, "Should return true when checking for content on the list")

	valid = IsMicroTTLContent("51120")
	assert.False(t, valid, "Should return false when checking for content not on the list")

	valid = IsMicroTTLContent("")
	assert.False(t, valid, "Should return false when checking for empty keys")
}
