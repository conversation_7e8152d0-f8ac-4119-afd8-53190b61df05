package lib

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"os/signal"
	"reflect"
	"strings"
	"syscall"

	jsoniter "github.com/json-iterator/go"
)

//go:generate mockery
var json = jsoniter.ConfigFastest

// StdJSON is the default json serializer
var StdJSON = jsoniter.ConfigCompatibleWithStandardLibrary

// ABCService is the type for ABC services which handled by Terminus
type ABCService = string

// Action describe the type of actions which trigger the request
type Action = string

// RequestType describe if the type is content or live or search
type RequestType string

const (
	// Capi service
	Capi ABCService = "capi"
	// Capi3 service
	Capi3 ABCService = "capi3"
	// Coremedia aka Capi service
	Coremedia ABCService = "coremedia"
	// Mapi service
	Mapi ABCService = "mapi"
	// Papi service
	Papi ABCService = "papi"
	// IView service
	IView ABCService = "iview"
	// Unearthed service
	Unearthed ABCService = "unearthed"

	//used to distinguish between REST and GQL when functions need to behave slightly differently.
	ENDPOINT_REST = "rest"
	ENDPOINT_GQL  = "gql"
)

// Universial action define for action of notification
const (
	ActionCreate  Action = "create"
	ActionUpdate  Action = "update"
	ActionDelete  Action = "delete"
	ActionHDelete Action = "hdelete"
)

const (
	ContentRequest   RequestType = "content"
	SearchRequest    RequestType = "search"
	TaxonomyRequest  RequestType = "taxonomy"
	LiveRequest      RequestType = "live"
	URLLookupRequest RequestType = "urllookup"
)

const (
	TypeContent         = "content"
	TypeSearch          = "search"
	TypeTaxonomy        = "taxonomy"
	TypeURLLookup       = "urllookup"
	TypeTeasableContent = "teasablecontent"
)

// UpstreamData abstracts common information of all data from upstream services
type UpstreamData interface {
	ContentID() string
	ContentType() string
	ContentSource() ABCService
}

type NoReport interface {
	NoReport() bool
}

// JoinURL is for join url paths to single url. It will add missing / or remove duplicate /
func JoinURL(paths ...string) string {
	result := strings.Builder{}
	for i, path := range paths {
		if i > 0 && !strings.HasPrefix(path, "/") {
			result.WriteRune('/')
		}
		if strings.HasSuffix(path, "/") {
			result.WriteString(path[:len(path)-1])
		} else {
			result.WriteString(path)
		}
	}
	return result.String()
}

// JoinPath returns path string with params
func JoinPath(path, params string) (string, error) {
	uri, err := url.Parse(path)
	if err != nil {
		return "", err
	}

	uri.RawQuery = params
	return uri.String(), nil
}

type Invoker interface {
	Invoke(context.Context, interface{}) ([]byte, error)
}

// TaskDone is the function for trace task has been done
type TaskDone func() error

type Request interface {
	CacheKey() string
	DocType() string
	FunctionName() string
	Path() (string, error)
	ContentSource() string
	RequestID() string
	DocID() string
	IsFromNotice() bool
	GetAction() (Action, error)
	RequestType() RequestType
}

// Indicate the request is for getting single resource. This type of reqeust should be able to return canonical URI
type ResourceRequest interface {
	Request
	URI() string
}

type Content interface {
	DocType() string
	DocID() string
}

type Search interface {
	DocType() string
	QueryParams() url.Values
}

// IsSet uses reflection to determine if an item is empty type agnostic
func IsSet(x interface{}) bool {
	return !reflect.DeepEqual(x, reflect.Zero(reflect.TypeOf(x)).Interface())
}

// ListenShutdownSignal function will listen syscall.SIGINT, syscall.SIGTERM signal and run clean up function and shutdown with 130 code
// This function is suitable to quit some service program requires clean up operations.
func ListenShutdownSignal(cleanup func()) {
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigs
		cleanup()
		os.Exit(130)
	}()
}

// URI returns the cannonical uri
func URI(contentSource, contentType, id string) string {
	return fmt.Sprintf("%s://%s/%s", contentSource, contentType, id)
}

func GCD(x, y int) int {
	for y != 0 {
		x, y = y, x%y
	}
	return x
}
