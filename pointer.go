package lib

// BoolPtr returns bool pointer
func BoolPtr(x bool) *bool {
	return &x
}

// BoolVal extracts bool value from bool pointer
func BoolVal(x *bool) bool {
	return BoolValWithDefault(x, false)
}

// BoolValWithDefault extract bool value from bool pointer or return default value if nil
func BoolValWithDefault(x *bool, val bool) bool {
	if x == nil {
		return val
	}
	return *x
}

// IntPtr returns int pointer
func IntPtr(x int) *int {
	return &x
}

// Int32Ptr returns int32 pointer
func Int32Ptr(x int32) *int32 {
	return &x
}

// Int64Ptr returns int64 pointer
func Int64Ptr(x int64) *int64 {
	return &x
}

// IntVal extracts int value from int pointer
func IntVal(x *int) int {
	return IntValWithDefault(x, 0)
}

// StringPtr returns string pointer
func StringPtr(x string) *string {
	return &x
}

// IntValWithDefault extract int value from int pointer or return default value if nil
func IntValWithDefault(x *int, val int) int {
	if x == nil {
		return val
	}
	return *x
}
