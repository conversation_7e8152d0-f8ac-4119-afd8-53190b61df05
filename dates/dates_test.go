package dates

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestTransformInputDateformat(t *testing.T) {
	t.Run("ISO8601 string and terminus url input format", func(t *testing.T) {
		t.Run("should return date string and no error", func(t *testing.T) {
			foo, err := TransformInputDateformat("2018-04-23T14:12:00+10:00", DateTimeFormatMAPIURLParam, false)
			assert.NoError(t, err)
			assert.Equal(t, "2018-04-23T04:12:00", foo)
		})
		t.Run("should return date string string with extend 59 sec and no error", func(t *testing.T) {
			foo, err := TransformInputDateformat("2018-04-23T14:12:00+10:00", DateTimeFormatMAPIURLParam, true)
			assert.NoError(t, err)
			assert.Equal(t, "2018-04-23T04:12:59", foo)
		})
		t.Run("should return error for incorrect format", func(t *testing.T) {
			_, err := TransformInputDateformat("2018-04-23T14:12:00", DateTimeFormatMAPIURLParam, true)
			assert.Error(t, err)
		})
		t.Run("should return error for seconds not 00", func(t *testing.T) {
			_, err := TransformInputDateformat("2018-04-23T14:12:05+10:00", DateTimeFormatMAPIURLParam, true)
			assert.Error(t, err)
		})
	})
}

func TestTransformDate(t *testing.T) {

	t.Run("Perfect Date Time String without seconds", func(t *testing.T) {
		t.Run("should not return error and should add :00 for the seconds", func(t *testing.T) {
			foo, err := TransformDate("2018-04-23T13:41", DateTimeFormatTerminusURLParam, DateTimeFormatMAPIURLParam)
			assert.NoError(t, err)
			assert.Equal(t, "2018-04-23T13:41:00", foo)
		})
	})

	t.Run("ISO8601 string and terminus url input format", func(t *testing.T) {
		t.Run("should return date string same as input and an error", func(t *testing.T) {
			foo, err := TransformDate("2018-04-23T14:12:04", DateTimeFormatTerminusURLParam, DateTimeFormatMAPIURLParam)
			assert.Error(t, err)
			assert.Equal(t, "2018-04-23T14:12:04", foo)
		})
	})

	t.Run("ISO8601 string and matching input format", func(t *testing.T) {
		t.Run("should not return error and should return date string with seconds stripped out", func(t *testing.T) {
			foo, err := TransformDate("2018-04-23T14:17:59", DateTimeFormatMAPIURLParam, DateTimeFormatTerminusURLParam)
			assert.NoError(t, err)
			assert.Equal(t, "2018-04-23T14:17", foo)
		})
	})

	t.Run("transforming terminus date string to coremedia format ie ISO8601 UTC", func(t *testing.T) {
		t.Run("should return desired date string without error", func(t *testing.T) {
			foo, err := TransformDate("2018-04-23T14:17", DateTimeFormatTerminusURLParam, DateTimeFormatCAPIV2URLParam)
			assert.NoError(t, err)
			assert.Equal(t, "2018-04-23T14:17:00Z", foo)
		})
	})

	t.Run("ISO8601 string and unmatched terminus url input format", func(t *testing.T) {
		t.Run("should return date string same as input and an error", func(t *testing.T) {
			foo, err := TransformDate("2018-04-23T14:17:05", DateTimeFormatTerminusURLParam, DateTimeFormatCAPIV2URLParam)
			assert.Error(t, err)
			assert.Equal(t, "2018-04-23T14:17:05", foo)
		})
	})

	t.Run("ISO8601 string (UTC) and matching input format", func(t *testing.T) {
		t.Run("should return desired date string without error", func(t *testing.T) {
			foo, err := TransformDate("2018-04-23T14:17:05Z", DateTimeFormatCAPIV2URLParam, DateTimeFormatTerminusURLParam)
			assert.NoError(t, err)
			assert.Equal(t, "2018-04-23T14:17", foo)
		})
	})

	t.Run("ISO8601 string (with timezone) and matching input format", func(t *testing.T) {
		t.Run("should return desired date string without error", func(t *testing.T) {
			foo, err := TransformDate("2018-04-23T14:17:05+10:00", DateTimeFormatCAPIV2URLParam, DateTimeFormatMAPIURLParam)
			assert.NoError(t, err)
			assert.Equal(t, "2018-04-23T04:17:05", foo)
		})
	})
}

func TestCalculateMaxAge(t *testing.T) {
	t.Run("Should return correct age base on correct input", func(t *testing.T) {
		now := time.Now().UTC()
		format := "2006-01-02T15:04:05"
		nextUpdated := now.Add(50 * time.Second).Format(format)
		age := CalculateMaxAge(nextUpdated, format, 10)
		assert.Equal(t, 49, age)
	})
	t.Run("Shoud return default value if incorrect", func(t *testing.T) {
		now := time.Now().UTC()
		format := "2006-01-02T15:04:05"
		nextUpdated := now.Add(50 * time.Second).Format("2006-01-02 15:04:05")
		age := CalculateMaxAge(nextUpdated, format, 10)
		assert.Equal(t, 10, age)
	})
	t.Run("Shoud return default value if calculated value less than default", func(t *testing.T) {
		now := time.Now().UTC()
		format := "2006-01-02T15:04:05"
		nextUpdated := now.Add(10 * time.Second).Format(format)
		age := CalculateMaxAge(nextUpdated, format, 20)
		assert.Equal(t, 20, age)
	})
	t.Run("Should return the correct max-age when a valid nextUpdated is passed in", func(t *testing.T) {
		nextUpdated := time.Now().UTC().Add(time.Second * time.Duration(60)).Format(DateTimeFormatTerminusBody)
		maxAge := CalculateMaxAge(nextUpdated, DateTimeFormatTerminusBody, 3)
		assert.True(t, maxAge > 58 && maxAge <= 60)
	})

	t.Run("Should return the default maxage if NextUpdated is less than 3 seconds in the future", func(t *testing.T) {
		nextUpdated := "2018-09-07T02:03:30"
		maxAge := CalculateMaxAge(nextUpdated, DateTimeFormatTerminusBody, 3)
		assert.Equal(t, 3, maxAge)
		// Expect(maxAge).To(BeEquivalentTo(3))
	})
}

func TestParseTTL(t *testing.T) {
	t.Run("should return correct ttl for second", func(t *testing.T) {
		ttl := "15s"
		expectedTTL := float64(15)
		result, err := ParseTTL(ttl)
		assert.NoError(t, err)
		assert.Equal(t, expectedTTL, result.Seconds())
	})

	t.Run("should return correct ttl for minute", func(t *testing.T) {
		ttl := "5m"
		expectedTTL := float64(300)
		result, err := ParseTTL(ttl)
		assert.NoError(t, err)
		assert.Equal(t, expectedTTL, result.Seconds())
	})

	t.Run("should return correct ttl for hour", func(t *testing.T) {
		ttl := "3h"
		expectedTTL := float64(10800)
		result, err := ParseTTL(ttl)
		assert.NoError(t, err)
		assert.Equal(t, expectedTTL, result.Seconds())
	})

	t.Run("should return correct ttl for day", func(t *testing.T) {
		ttl := "1d"
		expectedTTL := float64(86400)
		result, err := ParseTTL(ttl)
		assert.NoError(t, err)
		assert.Equal(t, expectedTTL, result.Seconds())
	})

	t.Run("should return error for empty ttl", func(t *testing.T) {
		ttl := ""
		expectedTTL := float64(0)
		result, err := ParseTTL(ttl)
		assert.Error(t, err)
		assert.Equal(t, expectedTTL, result.Seconds())
	})

	t.Run("should return error for incorrect value", func(t *testing.T) {
		ttl := "1x"
		expectedTTL := float64(0)
		result, err := ParseTTL(ttl)
		assert.Error(t, err)
		assert.Equal(t, expectedTTL, result.Seconds())
	})

	t.Run("should return error for incorrect value", func(t *testing.T) {
		ttl := "ss"
		expectedTTL := float64(0)
		result, err := ParseTTL(ttl)
		assert.Error(t, err)
		assert.Equal(t, expectedTTL, result.Seconds())
	})
}
