package dates

import (
	"fmt"
	"reflect"
	"strings"
	"time"
	"unsafe"

	"github.com/modern-go/reflect2"
)

const (
	TagRest   = "terminus_rest"
	TagStd    = "terminus"
	TagLegacy = "terminus_legacy"
	TagGQL    = "terminus_gql"
)

// ReformatDates is for reformat date fields into DateTimeFormatTerminusBody format in any structs
// if the field has the tag `$tag:"date_reformat($sourceformat,$targetformat)"`
// $format accept following values:
// mapi: default mapi date format in struct. check const DateTimeFormatMAPIBody
// papi: default mapi date format in struct. check const DateTimeFormatPAPIBody
// ad-hoc format: Any format defined by [golang date format](https://golang.org/pkg/time/#Parse) such as `2 Jan 2006 3:4` etc .
func ReformatDates(inst interface{}, tagName string) error {
	typ := reflect2.TypeOf(inst)
	if typ.Kind() != reflect.Ptr {
		return fmt.Errorf("Type should be pointer of struct but actual type is: %s", typ.String())
	}
	typ = typ.(reflect2.PtrType).Elem()
	if typ.Kind() != reflect.Struct {
		return fmt.Errorf("Type should be pointer of struct but actual type is: %s", typ.String())
	}
	dataType := typ.(reflect2.StructType)
	ptr := reflect2.PtrOf(inst)
	return reformatStruct(ptr, dataType, tagName)
}

// scan into struct and check date_reformat tag for reformat date formats.
func reformatStruct(ptr unsafe.Pointer, dataType reflect2.StructType, tagName string) error {
	numField := dataType.NumField()
	for i := 0; i < numField; i++ {
		field := dataType.Field(i)
		fieldType := field.Type()
		kind := fieldType.Kind()
		switch kind {
		case reflect.String: // only care about format in string fields
			source, target, tz := getReformatMeta(field, tagName)
			if source == "" { // non GQL use thr first layout. if layout is empty means no reformat
				continue
			}
			fieldPtr := field.UnsafeGet(ptr)
			value := (*string)(fieldPtr)
			// ignore empty value
			if value == nil || *value == "" {
				continue
			}
			// reformat
			parsed, err := time.Parse(source, *value)
			if err != nil {
				return err
			}
			if tz != nil {
				parsed = parsed.In(tz)
			}
			converted := parsed.Format(target)
			// set new format back to struct
			field.UnsafeSet(ptr, unsafe.Pointer(&converted))
		case reflect.Struct:
			err := reformatStruct(field.UnsafeGet(ptr), fieldType.(reflect2.StructType), tagName)
			if err != nil {
				return err
			}
		case reflect.Ptr:
			err := reformatPtr(field.UnsafeGet(ptr), fieldType.(reflect2.PtrType), tagName)
			if err != nil {
				return err
			}
		case reflect.Slice:
			err := reformatSlice(field.UnsafeGet(ptr), fieldType.(reflect2.SliceType), tagName)
			if err != nil {
				return err
			}
		case reflect.Array:
			err := reformatArray(field.UnsafeGet(ptr), fieldType.(reflect2.ArrayType), tagName)
			if err != nil {
				return err
			}
		case reflect.Map:
			err := reformatMap(field.UnsafeGet(ptr), fieldType.(reflect2.MapType), tagName)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func getReformatMeta(field reflect2.StructField, tagName string) (source, target string, tz *time.Location) {
	tagInfo := field.Tag().Get(tagName)
	if tagInfo == "" {
		return "", "", nil
	}
	metas := strings.Split(tagInfo, " ")
	for _, meta := range metas {
		if strings.HasPrefix(meta, "date_reformat(") {
			// get reformat layout
			param := meta[len("date_reformat(") : len(meta)-1]
			split := strings.Split(param, ",")
			if len(split) > 2 {
				return selectFormat(strings.TrimSpace(split[0])), selectFormat(strings.TrimSpace(split[1])), timezone(split[2])
			}
			return selectFormat(strings.TrimSpace(split[0])), selectFormat(strings.TrimSpace(split[1])), nil
		}
	}
	return "", "", nil
}

func timezone(tz string) *time.Location {
	location, err := time.LoadLocation(tz)
	if err != nil {
		return time.UTC
	}
	return location
}

func reformatSlice(ptr unsafe.Pointer, dataType reflect2.SliceType, tagName string) error {
	length := dataType.UnsafeLengthOf(ptr)
	if length == 0 {
		return nil
	}
	innerType := dataType.Elem()
	inner := innerType.Kind()
	// scan into slice items if item type is struct or ptr to struct
	switch inner {
	case reflect.Struct:
		for i := 0; i < length; i++ {
			childPtr := dataType.UnsafeGetIndex(ptr, i)
			err := reformatStruct(childPtr, innerType.(reflect2.StructType), tagName)
			if err != nil {
				return err
			}
		}
	case reflect.Ptr:
		innerInnerType := innerType.(reflect2.PtrType).Elem()
		innerInner := innerInnerType.Kind()
		if innerInner == reflect.Struct {
			for i := 0; i < length; i++ {
				temp := dataType.UnsafeGetIndex(ptr, i)
				if reflect2.IsNil(innerType.UnsafeIndirect(temp)) {
					continue
				}
				childPtr := *(*unsafe.Pointer)(temp)
				err := reformatStruct(childPtr, innerInnerType.(reflect2.StructType), tagName)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func reformatArray(ptr unsafe.Pointer, dataType reflect2.ArrayType, tagName string) error {
	length := dataType.Len()
	if length == 0 {
		return nil
	}
	innerType := dataType.Elem()
	inner := innerType.Kind()
	// scan into array items if item type is struct or ptr to struct
	switch inner {
	case reflect.Struct:
		for i := 0; i < length; i++ {
			childPtr := dataType.UnsafeGetIndex(ptr, i)
			err := reformatStruct(childPtr, innerType.(reflect2.StructType), tagName)
			if err != nil {
				return err
			}
		}
	case reflect.Ptr:
		innerInnerType := innerType.(reflect2.PtrType).Elem()
		innerInner := innerInnerType.Kind()
		if innerInner == reflect.Struct {
			for i := 0; i < length; i++ {
				temp := dataType.UnsafeGetIndex(ptr, i)
				if reflect2.IsNil(innerType.UnsafeIndirect(temp)) {
					continue
				}
				childPtr := *(*unsafe.Pointer)(temp)
				err := reformatStruct(childPtr, innerInnerType.(reflect2.StructType), tagName)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func reformatMap(ptr unsafe.Pointer, dataType reflect2.MapType, tagName string) error {
	innerType := dataType.Elem()
	inner := innerType.Kind()
	itor := dataType.UnsafeIterate(ptr)
	// scan into map value items if item type is struct or ptr to struct. we dont consider the key type for now
	switch inner {
	case reflect.Struct:
		for itor.HasNext() {
			_, childPtr := itor.UnsafeNext()
			err := reformatStruct(childPtr, innerType.(reflect2.StructType), tagName)
			if err != nil {
				return err
			}
		}
	case reflect.Ptr:
		innerInnerType := innerType.(reflect2.PtrType).Elem()
		innerInner := innerInnerType.Kind()
		if innerInner == reflect.Struct {
			for itor.HasNext() {
				_, childPtr := itor.UnsafeNext()
				if reflect2.IsNil(innerType.UnsafeIndirect(childPtr)) {
					continue
				}
				childPtr = *(*unsafe.Pointer)(childPtr)
				err := reformatStruct(childPtr, innerInnerType.(reflect2.StructType), tagName)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func reformatPtr(ptr unsafe.Pointer, dataType reflect2.PtrType, tagName string) error {
	inner := dataType.Elem().Kind()
	if inner == reflect.Struct {
		temp := (*unsafe.Pointer)(ptr)
		if temp == nil || reflect2.IsNil(dataType.UnsafeIndirect(ptr)) {
			return nil
		}
		fieldPtr := *temp
		err := reformatStruct(fieldPtr, dataType.Elem().(reflect2.StructType), tagName)
		if err != nil {
			return err
		}
	}
	return nil
}

func selectFormat(param string) string {
	layout := param
	switch param {
	case "std":
		layout = DateTimeFormatTerminusGQL
	case "rest":
		layout = DateTimeFormatTerminusBody
	case "capi":
		layout = DateTimeFormatCAPIV2Body
	case "mapi":
		layout = DateTimeFormatMAPIBody
	case "rest_legacy":
		layout = DateTimeFormatTerminusLegacyBody
	case "papi":
		layout = DateTimeFormatPAPIBody
	case "iview":
		layout = DateTimeFormatIviewBody
	}
	return layout
}
