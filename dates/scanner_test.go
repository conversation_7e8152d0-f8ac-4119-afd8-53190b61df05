package dates

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type Play struct {
	Arid        string `json:"arid,omitempty"`
	Entity      string `json:"entity,omitempty"`
	PlayedTime  string `json:"played_time,omitempty" terminus:"date_reformat(papi,rest,UTC)"`
	StartedTime string `json:"started_time,omitempty" terminus:"date_reformat(papi,rest,UTC)"`
	ServiceID   string `json:"service_id,omitempty"`
}

type Parent struct {
	Total int  `json:"total,omitempty"`
	Item  Play `json:"item,omitempty"`
}

type ParentPtr struct {
	Total int   `json:"total,omitempty"`
	Item  *Play `json:"item,omitempty"`
}

type ParentSlice struct {
	Total int    `json:"total,omitempty"`
	Items []Play `json:"item,omitempty"`
}
type ParentSlicePtr struct {
	Total int     `json:"total,omitempty"`
	Items []*Play `json:"item,omitempty"`
}
type ParentMap struct {
	Total int             `json:"total,omitempty"`
	Items map[string]Play `json:"item,omitempty"`
}
type ParentMapPtr struct {
	Total int              `json:"total,omitempty"`
	Items map[string]*Play `json:"item,omitempty"`
}

func TestReformat(t *testing.T) {
	t.Run("Should set correctly on direct struct", func(t *testing.T) {
		p := &Play{
			Arid:        "123",
			Entity:      "play",
			PlayedTime:  "2018-08-19T02:06:51+0000",
			StartedTime: "2018-08-20T13:35:50+1000",
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.PlayedTime)
		assert.Equal(t, "2018-08-20T03:35:50+00:00", p.StartedTime)
	})
	t.Run("Should set correctly in embedded struct", func(t *testing.T) {
		p := &Parent{
			Item: Play{
				Arid:        "123",
				Entity:      "play",
				PlayedTime:  "2018-08-19T02:06:51+0000",
				StartedTime: "2018-08-20T13:35:50+1000",
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Item.PlayedTime)
		assert.Equal(t, "2018-08-20T03:35:50+00:00", p.Item.StartedTime)
	})
	t.Run("Should set correctly in embedded pointer struct", func(t *testing.T) {
		p := &ParentPtr{
			Item: &Play{
				Arid:        "123",
				Entity:      "play",
				PlayedTime:  "2018-08-19T02:06:51+0000",
				StartedTime: "2018-08-20T13:35:50+1000",
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Item.PlayedTime)
		assert.Equal(t, "2018-08-20T03:35:50+00:00", p.Item.StartedTime)
	})
	t.Run("Should not error in embedded nil pointer struct", func(t *testing.T) {
		p := &ParentPtr{}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Nil(t, p.Item)
	})
	t.Run("Should set correctly in embedded slice struct", func(t *testing.T) {
		p := &ParentSlice{
			Items: []Play{
				{
					Arid:        "123",
					Entity:      "play",
					PlayedTime:  "2018-08-19T02:06:51+0000",
					StartedTime: "2018-08-20T13:35:50+1000",
				},
				{
					Arid:        "456",
					Entity:      "play",
					PlayedTime:  "2018-08-29T02:06:51+0000",
					StartedTime: "2018-08-30T13:35:50+1000",
				},
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Items[0].PlayedTime)
		assert.Equal(t, "2018-08-20T03:35:50+00:00", p.Items[0].StartedTime)
		assert.Equal(t, "2018-08-29T02:06:51+00:00", p.Items[1].PlayedTime)
		assert.Equal(t, "2018-08-30T03:35:50+00:00", p.Items[1].StartedTime)
	})
	t.Run("Should set correctly in embedded slice pointer struct", func(t *testing.T) {
		p := &ParentSlicePtr{
			Items: []*Play{
				{
					Arid:       "123",
					Entity:     "play",
					PlayedTime: "2018-08-19T02:06:51+0000",
				},
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Items[0].PlayedTime)
	})
	t.Run("Should not error in embedded slice nil pointer struct", func(t *testing.T) {
		p := &ParentSlicePtr{
			Items: []*Play{
				{
					Arid:       "123",
					Entity:     "play",
					PlayedTime: "2018-08-19T02:06:51+0000",
				},
				nil,
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Items[0].PlayedTime)
		assert.Nil(t, p.Items[1])
	})
	t.Run("Should set correctly in embedded map struct", func(t *testing.T) {
		p := &ParentMap{
			Items: map[string]Play{
				"1": {
					Arid:       "123",
					Entity:     "play",
					PlayedTime: "2018-08-19T02:06:51+0000",
				},
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Items["1"].PlayedTime)
	})
	t.Run("Should set correctly in embedded map ptr struct", func(t *testing.T) {
		p := &ParentMapPtr{
			Items: map[string]*Play{
				"1": {
					Arid:       "123",
					Entity:     "play",
					PlayedTime: "2018-08-19T02:06:51+0000",
				},
				"2": {
					Arid:       "456",
					Entity:     "play",
					PlayedTime: "2018-08-29T02:06:51+0000",
				},
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Items["1"].PlayedTime)
		assert.Equal(t, "2018-08-29T02:06:51+00:00", p.Items["2"].PlayedTime)
	})
	t.Run("Should not error in embedded map nil pointer struct", func(t *testing.T) {
		p := &ParentMapPtr{
			Items: map[string]*Play{
				"1": {
					Arid:       "123",
					Entity:     "play",
					PlayedTime: "2018-08-19T02:06:51+0000",
				},
				"2": {
					Arid:       "456",
					Entity:     "play",
					PlayedTime: "2018-08-29T02:06:51+0000",
				},
				"3": nil,
			},
		}
		err := ReformatDates(p, "terminus")
		assert.NoError(t, err)
		assert.Equal(t, "2018-08-19T02:06:51+00:00", p.Items["1"].PlayedTime)
		assert.Equal(t, "2018-08-29T02:06:51+00:00", p.Items["2"].PlayedTime)
		assert.Nil(t, p.Items["3"])
	})
}
