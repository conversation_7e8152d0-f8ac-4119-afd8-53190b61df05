package lib

import (
	"fmt"
	"strconv"
	"strings"
)

var slugReplacer = strings.NewReplacer(
	" ", "-",
	",", "",
	"(", "",
	")", "",
	"[", "",
	"]", "",
	".", "",
	"'", "",
	"’", "",
)

// Slug replaces ' ' to '-' and replace ',', '(', ')' to empty
func Slug(s string) string {
	s = strings.ToLower(s)
	return slugReplacer.Replace(s)
}

// ID returns string for interface
func ID(v interface{}) (string, error) {
	switch v := v.(type) {
	case string:
		return v, nil
	case int:
		return strconv.Itoa(v), nil
	case float64:
		return strconv.FormatFloat(float64(v), 'f', 0, 64), nil
	case nil:
		return "", nil
	default:
		return "", fmt.Errorf("%T is not a string", v)
	}
}
