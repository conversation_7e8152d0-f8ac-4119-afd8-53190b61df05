package graphql

import (
	"crypto/sha256"
	"encoding/hex"
	"strings"
)

func ComputeTrimHash(str string) string {
	s := TrimStringLines(str)
	return ComputeHash(s)
}

// trim string with multiple lines into one line
func TrimStringLines(str string) string {
	// remove newlines
	q := strings.ReplaceAll(str, "\n", " ")
	// remove dup whitespace to one
	s := spaceRegexp.ReplaceAllString(q, " ")
	return s
}

func ComputeHash(str string) string {
	b := sha256.Sum256([]byte(str))
	return hex.EncodeToString(b[:])

}
