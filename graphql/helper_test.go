package graphql

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestComputeTrimHash(t *testing.T) {
	s1 := `test
	test   test`
	s2 := `test test test`

	assert.Equal(t, ComputeTrimHash(s1), ComputeTrimHash(s2))
}

func TestTrimStringLines(t *testing.T) {
	s := `test
	test`
	expected := `test test`

	assert.NotEqual(t, s, expected)
	assert.Equal(t, TrimStringLines(s), expected)
}

func TestComputeHash(t *testing.T) {
	s := "test"
	expected := "9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08"
	assert.Equal(t, ComputeHash(s), expected)
}
