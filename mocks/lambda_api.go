// Code generated by mockery v2.52.3. DO NOT EDIT.

package mocks

import (
	context "context"

	lambda "github.com/aws/aws-sdk-go-v2/service/lambda"
	mock "github.com/stretchr/testify/mock"
)

// MockLambdaAPI is an autogenerated mock type for the LambdaAPI type
type MockLambdaAPI struct {
	mock.Mock
}

type MockLambdaAPI_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLambdaAPI) EXPECT() *MockLambdaAPI_Expecter {
	return &MockLambdaAPI_Expecter{mock: &_m.Mock}
}

// Invoke provides a mock function with given fields: ctx, params, optFuncs
func (_m *MockLambdaAPI) Invoke(ctx context.Context, params *lambda.InvokeInput, optFuncs ...func(*lambda.Options)) (*lambda.InvokeOutput, error) {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Invoke")
	}

	var r0 *lambda.InvokeOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *lambda.InvokeInput, ...func(*lambda.Options)) (*lambda.InvokeOutput, error)); ok {
		return rf(ctx, params, optFuncs...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *lambda.InvokeInput, ...func(*lambda.Options)) *lambda.InvokeOutput); ok {
		r0 = rf(ctx, params, optFuncs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*lambda.InvokeOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *lambda.InvokeInput, ...func(*lambda.Options)) error); ok {
		r1 = rf(ctx, params, optFuncs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockLambdaAPI_Invoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Invoke'
type MockLambdaAPI_Invoke_Call struct {
	*mock.Call
}

// Invoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params *lambda.InvokeInput
//   - optFuncs ...func(*lambda.Options)
func (_e *MockLambdaAPI_Expecter) Invoke(ctx interface{}, params interface{}, optFuncs ...interface{}) *MockLambdaAPI_Invoke_Call {
	return &MockLambdaAPI_Invoke_Call{Call: _e.mock.On("Invoke",
		append([]interface{}{ctx, params}, optFuncs...)...)}
}

func (_c *MockLambdaAPI_Invoke_Call) Run(run func(ctx context.Context, params *lambda.InvokeInput, optFuncs ...func(*lambda.Options))) *MockLambdaAPI_Invoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]func(*lambda.Options), len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(func(*lambda.Options))
			}
		}
		run(args[0].(context.Context), args[1].(*lambda.InvokeInput), variadicArgs...)
	})
	return _c
}

func (_c *MockLambdaAPI_Invoke_Call) Return(_a0 *lambda.InvokeOutput, _a1 error) *MockLambdaAPI_Invoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockLambdaAPI_Invoke_Call) RunAndReturn(run func(context.Context, *lambda.InvokeInput, ...func(*lambda.Options)) (*lambda.InvokeOutput, error)) *MockLambdaAPI_Invoke_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockLambdaAPI creates a new instance of MockLambdaAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLambdaAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLambdaAPI {
	mock := &MockLambdaAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
