package lib

import (
	gj "encoding/json"
	"errors"
	"reflect"
	"time"
)

// This type will be used to hold the raw json content binary array, it will benefit when logging the content
type RawJSON gj.RawMessage

func (r RawJSON) String() string {
	data, _ := json.Marshal(gj.RawMessage(r))
	return string(data)
}

func (r RawJSON) MarshalJSON() ([]byte, error) {
	return gj.RawMessage(r).MarshalJSON()
}

func (r *RawJSON) UnmarshalJSON(data []byte) error {
	if r == nil {
		return errors.New("json.RawMessage: UnmarshalJSON on nil pointer")
	}
	*r = append((*r)[0:0], data...)
	return nil
}

// Convert time to millisecond precision
func MillisecTime(t time.Time) string {
	return t.Format("2006-01-02T15:04:05.000Z")
}

func ParseMillisecTime(s string) (time.Time, error) {
	return time.Parse("2006-01-02T15:04:05.000Z", s)
}

func TypeName(i interface{}) string {
	t := reflect.TypeOf(i).String()
	if t[0] == '*' { // remove '*' if it's pointer
		return t[1:]
	}
	return t
}
