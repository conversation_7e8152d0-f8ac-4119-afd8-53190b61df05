package kontent

import (
	"strings"
	"sync"

	json "github.com/json-iterator/go"
)

// type field in Kontent System meta data which represent the type of the content
type KontentType = string

type ModularItem interface {
	SetModularContent(ModularContentMap)
	ParseElements(System, json.RawMessage) error
}

type System struct {
	ID               string        `json:"id,omitempty"`
	Name             string        `json:"name,omitempty"`
	Codename         string        `json:"codename,omitempty"`
	Language         string        `json:"language,omitempty"`
	Type             KontentType   `json:"type,omitempty"`
	Collection       string        `json:"collection,omitempty"`
	SitemapLocations []interface{} `json:"sitemap_locations,omitempty"`
	LastModified     string        `json:"last_modified,omitempty"`
	WorkflowStep     string        `json:"workflow_step,omitempty"`
}

type Text struct {
	Type  string `json:"type,omitempty"`
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

type Int struct {
	Type  string `json:"type,omitempty"`
	Name  string `json:"name,omitempty"`
	Value int64  `json:"value,omitempty"`
}

type Number struct {
	Type  string  `json:"type,omitempty"`
	Name  string  `json:"name,omitempty"`
	Value float64 `json:"value,omitempty"`
}

type Asset struct {
	Type  string        `json:"type,omitempty"`
	Name  string        `json:"name,omitempty"`
	Value []*AssetValue `json:"value,omitempty"`
}

type AssetValue struct {
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Type        string `json:"type,omitempty"`
	Size        int    `json:"size,omitempty"`
	URL         string `json:"url,omitempty"`
	Width       int    `json:"width,omitempty"`
	Height      int    `json:"height,omitempty"`
}

func (a Asset) GetValue() *AssetValue {
	if len(a.Value) == 0 {
		return nil
	}
	return a.Value[0]
}

type Taxonomy struct {
	Type          string `json:"type,omitempty"`
	Name          string `json:"name,omitempty"`
	TaxonomyGroup string `json:"taxonomy_group,omitempty"`
	Value         Values `json:"value,omitempty"`
}

func (t Taxonomy) GetValues() []string {
	return t.Value.GetValues()
}

func (t Taxonomy) GetCodenames() []string {
	return t.Value.GetCodenames()
}

type MultipleChoice struct {
	Type  string `json:"type,omitempty"`
	Name  string `json:"name,omitempty"`
	Value Values `json:"value,omitempty"`
}

func (c MultipleChoice) IsEmpty() bool {
	return len(c.Value) == 0
}

func (c MultipleChoice) GetValue() string {
	if len(c.Value) == 0 {
		return ""
	}

	return c.Value[0].Name
}

func (c MultipleChoice) GetBool() *bool {
	if len(c.Value) == 0 {
		return nil
	}

	b := false
	if strings.ToLower(c.Value[0].Codename) == "yes" {
		b = true
	}

	return &b
}

func (c MultipleChoice) GetValues() []string {
	return c.Value.GetValues()
}

func (c MultipleChoice) GetCodenames() []string {
	return c.Value.GetCodenames()
}

type Custom struct {
	Type  string `json:"type,omitempty"`
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

type Values []*Value

func (v Values) GetValues() []string {
	count := len(v)
	if count == 0 {
		return nil
	}

	result := make([]string, count)
	for k, v := range v {
		result[k] = v.Name
	}

	return result
}

func (v Values) GetCodenames() []string {
	count := len(v)
	if count == 0 {
		return nil
	}

	result := make([]string, count)
	for k, v := range v {
		result[k] = v.Codename
	}

	return result
}

type DateTime struct {
	Type  string `json:"type,omitempty"`
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

type Value struct {
	Name     string `json:"name,omitempty"`
	Codename string `json:"codename,omitempty"`
}

type Pagination struct {
	Count      int    `json:"count,omitempty"`
	Limit      int    `json:"limit,omitempty"`
	NextPage   string `json:"next_page,omitempty"`
	Skip       int    `json:"skip,omitempty"`
	TotalCount int    `json:"total_count,omitempty"`
}

func (p Pagination) Total() int {
	return p.TotalCount
}

func (p Pagination) Offset() int {
	return p.Skip
}

func (p Pagination) Size() int {
	return p.Count
}

type ModularContent struct {
	Type  string   `json:"type,omitempty"`
	Name  string   `json:"name,omitempty"`
	Value []string `json:"value,omitempty"`
}

func (c ModularContent) GetValue() string {
	if len(c.Value) == 0 {
		return ""
	}

	return c.Value[0]
}

type ModularContentMap map[string]*ModularContentItem

func (c ModularContentMap) GetItem(key string, convert func(i *ModularContentItem) (ModularItem, error)) (KontentType, ModularItem, error) {
	mc, ok := c[key] // read only so no need to consider concurrency
	if !ok {
		return "", nil, nil
	}
	var err error
	if mc.Parsed == nil {
		mc.once.Do(func() {
			mc.Parsed, err = convert(mc)
			if err != nil || mc.Parsed == nil {
				return
			}
			mc.Parsed.SetModularContent(c)
		})
	}
	return mc.System.Type, mc.Parsed, err
}

type Item struct {
	Item struct {
		System   System          `json:"system,omitempty"`
		Elements json.RawMessage `json:"elements,omitempty"`
	} `json:"item,omitempty"`
	ModularContent ModularContentMap `json:"modular_content,omitempty"`
}
type Items struct {
	Items          []ModularContentItem `json:"items,omitempty"`
	Pagination     Pagination           `json:"pagination,omitempty"`
	ModularContent ModularContentMap    `json:"modular_content,omitempty"`
}

type ModularContentItem struct {
	System   System          `json:"system,omitempty"`
	Elements json.RawMessage `json:"elements,omitempty"`
	Parsed   ModularItem     `json:"-"`
	once     *sync.Once      `json:"-"`
}

func (c *ModularContentItem) UnmarshalJSON(data []byte) error {
	s := &struct {
		System   System          `json:"system,omitempty"`
		Elements json.RawMessage `json:"elements,omitempty"`
	}{}
	err := json.Unmarshal(data, s)
	if err != nil {
		return err
	}
	c.once = &sync.Once{}
	c.System = s.System
	c.Elements = s.Elements
	return nil
}
