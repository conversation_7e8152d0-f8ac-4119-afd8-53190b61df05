package unearthed

import (
	json "github.com/json-iterator/go"

	"stash.abc-dev.net.au/ter/lib"
	"stash.abc-dev.net.au/ter/lib/kontent"
)

type Artists struct {
	Items          []*ArtistItem             `json:"items,omitempty"`
	Pagination     kontent.Pagination        `json:"pagination,omitempty"`
	ModularContent kontent.ModularContentMap `json:"modular_content,omitempty"`
}

type Artist struct {
	Item           *ArtistItem               `json:"item,omitempty"`
	ModularContent kontent.ModularContentMap `json:"modular_content,omitempty"`
}

func NewArtist() *Artist {
	return &Artist{
		Item: &ArtistItem{
			Elements: &ArtistElements{},
		},
	}
}

func (a Artist) IsUnearthedUser() {}

func (a Artist) IsEmpty() bool {
	if a.Item == nil || a.Item.Elements == nil {
		return true
	}
	return false
}

func (a *Artist) SetModularContent(c kontent.ModularContentMap) {
	a.<PERSON>onte<PERSON> = c
}

func (a *Artist) ParseElements(s kontent.System, d json.RawMessage) error {
	a.Item.System = s
	return json.Unmarshal([]byte(d), a.Item.Elements)
}

func (a Artist) Codename() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.System.Codename
}

func (a Artist) ContentType() string {
	return ArtistEntity
}

func (a Artist) ContentSource() string {
	return lib.Unearthed
}

func (a Artist) URI() string {
	if a.IsEmpty() {
		return ""
	}
	return lib.URI(a.ContentSource(), a.ContentType(), a.ID())
}

type ArtistItem struct {
	System   kontent.System  `json:"system,omitempty"`
	Elements *ArtistElements `json:"elements,omitempty"`
}

type ArtistElements struct {
	UserProfile
	Favourites

	SoundsLike          kontent.Text           `json:"sounds_like,omitempty"`
	Members             kontent.Text           `json:"members,omitempty"`
	Summary             kontent.Text           `json:"summary,omitempty"`
	Tags                kontent.Text           `json:"tags,omitempty"`
	ArtistPick          kontent.ModularContent `json:"artist_pick,omitempty"`
	FeaturedArtistWeek  kontent.DateTime       `json:"featured_artist___week,omitempty"`
	FeaturedArtistBlurb kontent.Text           `json:"featured_artist___blurb,omitempty"`
	UnearthedHigh       kontent.MultipleChoice `json:"unearthed_high,omitempty"`
	Indigenous          kontent.MultipleChoice `json:"indigenous,omitempty"`
	IndigenousDisplay   kontent.MultipleChoice `json:"indigenous_display,omitempty"`
	Disability          kontent.MultipleChoice `json:"disability,omitempty"`
}

func (a Artist) ID() string {
	if a.Item == nil {
		return ""
	}
	return a.Item.System.ID
}

func (a Artist) LastModified() (string, error) {
	if a.Item == nil {
		return "", nil
	}

	return transformDate(a.Item.System.LastModified)
}

// *** User Profile *** //
func (a Artist) ProfileName() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.ProfileName.Value
}

func (a Artist) Slug() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Slug.Value
}

func (a Artist) Genres() []string {
	if a.IsEmpty() {
		return nil
	}
	return a.Item.Elements.Genres.GetCodenames()
}

func (a Artist) Summary() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Summary.Value
}

func (a Artist) Bio() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Bio.Value
}

func (a Artist) Image() *kontent.AssetValue {
	if a.IsEmpty() {
		return nil
	}
	return a.Item.Elements.Image.GetValue()
}

func (a Artist) SoundsLike() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.SoundsLike.Value
}

// *** Social Links *** //
func (a Artist) SocialLinksTwitter() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Twitter.Value
}

func (a Artist) SocialLinksFacebook() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Facebook.Value
}

func (a Artist) SocialLinksInstagram() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Instagram.Value
}

func (a Artist) SocialLinksTiktok() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Tiktok.Value
}

func (a Artist) SocialLinksYoutube() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Youtube.Value
}

func (a Artist) SocialLinksSpotify() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Spotify.Value
}

func (a Artist) SocialLinksBandcamp() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Bandcamp.Value
}

func (a Artist) SocialLinksSoundcloud() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Soundcloud.Value
}

func (a Artist) SocialLinksAppleMusic() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.AppleMusic.Value
}

func (a Artist) SocialLinksWebsite() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Website.Value
}

func (a Artist) FeaturedArtistWeek() (string, error) {
	if a.IsEmpty() {
		return "", nil
	}
	return transformDate(a.Item.Elements.FeaturedArtistWeek.Value)
}

func (a Artist) FeaturedArtistBlurb() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.FeaturedArtistBlurb.Value
}

func (a Artist) Location() *Location {
	if a.IsEmpty() {
		return nil
	}
	return &a.Item.Elements.Location
}

func (a Artist) Tags() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Tags.Value
}

func (a Artist) LocationName() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Location.GetName()
}

func (a Artist) Region() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Location.GetRegion()
}

func (a Artist) StateName() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Location.GetState()
}

func (a Artist) StateCode() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Location.GetStateCode()
}

func (a Artist) Suburb() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Location.GetSuburb()
}

func (a Artist) Postcode() string {
	if a.IsEmpty() {
		return ""
	}
	return a.Item.Elements.Location.GetPostcode()
}

func (a Artist) UnearthedHigh() *bool {
	if a.IsEmpty() {
		return nil
	}

	return a.Item.Elements.UnearthedHigh.GetBool()
}

func (a Artist) Indigenous() *string {
	if a.IsEmpty() || a.Item.Elements.Indigenous.IsEmpty() {
		return nil
	}
	value := a.Item.Elements.Indigenous.GetValue()
	return &value
}

func (a Artist) IndigenousDisplay() *string {
	if a.IsEmpty() || a.Item.Elements.IndigenousDisplay.IsEmpty() {
		return nil
	}
	value := a.Item.Elements.IndigenousDisplay.GetValue()
	return &value
}

func (a Artist) Disability() *string {
	if a.IsEmpty() || a.Item.Elements.Disability.IsEmpty() {
		return nil
	}
	value := a.Item.Elements.Disability.GetValue()
	return &value
}
