package unearthed

import (
	json "github.com/json-iterator/go"

	"stash.abc-dev.net.au/ter/lib"
	libdates "stash.abc-dev.net.au/ter/lib/dates"
	"stash.abc-dev.net.au/ter/lib/kontent"
)

type User interface {
	IsUnearthedUser()
	ParseElements(kontent.System, json.RawMessage) error
}

// Entity is the type of Unearthed entity
type Entity = string

const (
	ArtistsEntity Entity = "artists"
	ArtistEntity  Entity = "artist"
	FansEntity    Entity = "fans"
	FanEntity     Entity = "fan"
	TracksEntity  Entity = "tracks"
	TrackEntity   Entity = "track"
	ReviewsEntity Entity = "reviews"
	ReviewEntity  Entity = "review"
	UsersEntity   Entity = "users"
	UserEntity    Entity = "user"
)

const (
	TypeArtist   kontent.KontentType = "artist_profile"
	TypeFan      kontent.KontentType = "fan_profile"
	TypeHomepage kontent.KontentType = "homepage"
	TypeTrack    kontent.KontentType = "track"
	TypeReview   kontent.KontentType = "review"
)

type UserProfile struct {
	ProfileName kontent.Text     `json:"user_profile_fields__profile_name,omitempty"`
	Slug        kontent.Text     `json:"user_profile_fields__slug,omitempty"`
	Bio         kontent.Text     `json:"user_profile_fields__bio,omitempty"`
	Image       kontent.Asset    `json:"user_profile_fields__image,omitempty"`
	Genres      kontent.Taxonomy `json:"user_profile_fields__genres,omitempty"`
	Location
	SocialLinks
}

type SocialLinks struct {
	Twitter    kontent.Text `json:"social_links__twitter,omitempty"`
	Facebook   kontent.Text `json:"social_links__facebook,omitempty"`
	Instagram  kontent.Text `json:"social_links__instagram,omitempty"`
	Tiktok     kontent.Text `json:"social_links__tiktok,omitempty"`
	Youtube    kontent.Text `json:"social_links__youtube,omitempty"`
	Spotify    kontent.Text `json:"social_links__spotify,omitempty"`
	Bandcamp   kontent.Text `json:"social_links__bandcamp,omitempty"`
	Soundcloud kontent.Text `json:"social_links__soundcloud,omitempty"`
	AppleMusic kontent.Text `json:"social_links__apple_music,omitempty"`
	Website    kontent.Text `json:"social_links__website,omitempty"`
}

type Favourites struct {
	FavouriteArtists kontent.ModularContent `json:"favourites__favourite_artists,omitempty"`
	CurrentlyLoving  kontent.ModularContent `json:"favourites__currently_loving,omitempty"`
	// *** Private Fields *** //
	// LovedTracks      KontentModularContent `json:"loved_tracks,omitempty"`
}

type Location struct {
	AuroraID             kontent.Text `json:"location__aurora_id,omitempty"`
	Name                 kontent.Text `json:"location__name,omitempty"`
	Suburb               kontent.Text `json:"location__suburb,omitempty"`
	Postcode             kontent.Text `json:"location__postcode,omitempty"`
	StateID              kontent.Text `json:"location__state_id,omitempty"`
	StateName            kontent.Text `json:"location__state_name,omitempty"`
	CountryID            kontent.Text `json:"location__country_id,omitempty"`
	CountryName          kontent.Text `json:"location__country_name,omitempty"`
	Region               kontent.Text `json:"location__region,omitempty"`
	ABCRegionName        kontent.Text `json:"location__abc_region_name,omitempty"`
	ABCRegionServiceName kontent.Text `json:"location__abc_region_service_name,omitempty"`
	ABCRegionBrandName   kontent.Text `json:"location__abc_region_brand_name,omitempty"`
	Lat                  kontent.Text `json:"location__lat,omitempty"`
	Long                 kontent.Text `json:"location__long,omitempty"`
}

func (l Location) GetName() string {
	return l.Name.Value
}

func (l Location) GetRegion() string {
	return l.ABCRegionName.Value
}

func (l Location) GetState() string {
	return l.StateName.Value
}

func (l Location) GetStateCode() string {
	return l.StateID.Value
}

func (l Location) GetSuburb() string {
	return l.Suburb.Value
}

func (l Location) GetPostcode() string {
	return l.Postcode.Value
}

type SourceFile struct {
	ID         string `json:"id,omitempty"`
	DurationMs int    `json:"durationMs,omitempty"`
	FileName   string `json:"fileName,omitempty"`
	Size       int    `json:"fileSize,omitempty"`
	URL        string `json:"url,omitempty"`
}

func ParseSourceFile(jsonStr string) *SourceFile {
	f := &SourceFile{}
	err := json.Unmarshal([]byte(jsonStr), f)
	if err != nil {
		return nil
	}
	return f
}

func Model(entity Entity) kontent.ModularItem {
	switch entity {
	case ArtistEntity:
		return NewArtist()
	case FanEntity:
		return NewFan()
	case TrackEntity:
		return NewTrack()
	case ReviewEntity:
		return NewReview()
	}
	return nil
}

func EntityByType(t kontent.KontentType) Entity {
	switch t {
	case TypeArtist:
		return ArtistEntity
	case TypeFan:
		return FanEntity
	case TypeTrack:
		return TrackEntity
	case TypeReview:
		return ReviewEntity
	}
	return ""
}

func ModelByType(t kontent.KontentType) (Entity, kontent.ModularItem) {
	entity := EntityByType(t)
	return entity, Model(entity)
}

// UserModelByType returns User type by KontentType
func UserModelByType(t kontent.KontentType) (Entity, User) {
	switch t {
	case TypeArtist:
		return ArtistEntity, NewArtist()
	case TypeFan:
		return FanEntity, NewFan()
	}
	return "", nil
}

func ConvertToModularItem(i *kontent.ModularContentItem) (kontent.ModularItem, error) {
	_, d := ModelByType(i.System.Type)
	err := d.ParseElements(i.System, i.Elements)
	return d, err
}

// ParseLimit returns default limit if given limit is nil or 0
func ParseLimit(limit *int) int {
	limitVal := lib.IntVal(limit)
	if limitVal <= 0 {
		return DEFAULT_LIMIT
	}
	return limitVal
}

func transformDate(str string) (string, error) {
	return libdates.TransformDate(str, libdates.DateTimeFormatKontentBody, libdates.DateTimeFormatTerminusGQL)
}
