package mapi

import (
	"math"
	"time"

	"stash.abc-dev.net.au/ter/lib"

	"stash.abc-dev.net.au/ter/lib/dates"
)

// Play contains the result from mapi play
type Play struct {
	Object
	Count      int          `json:"count,omitempty"`
	PlayedTime string       `json:"played_time,omitempty" terminus_rest:"date_reformat(mapi,rest_legacy,UTC)" terminus_gql:"date_reformat(mapi,std,UTC)"`
	Recording  *Recording   `json:"recording,omitempty"`
	Release    *Release     `json:"release,omitempty"`
	ServiceID  string       `json:"service_id,omitempty"`
	Summary    TrackSummary `json:"summary,omitempty"` // The business name is `track metadata`
}

func (p *Play) Process() {}
func (Play) IsMapi()     {}

func (p *Play) IsEmpty() bool {
	if p == nil {
		return true
	}

	if *p == (Play{}) {
		return true
	}

	return false
}

// Plays contains the results of play search
type Plays struct {
	Total  *int    `json:"total,omitempty"`
	Offset *int    `json:"offset,omitempty"`
	Limit  *int    `json:"limit,omitempty"`
	Items  []*Play `json:"items,omitempty"`
}

// GetTotal returns total int value
func (p Plays) GetTotal() int {
	return lib.IntVal(p.Total)
}

// GetOffset returns offset int value
func (p Plays) GetOffset() int {
	return lib.IntVal(p.Offset)
}

// GetLimit returns limit int value
func (p Plays) GetLimit() int {
	return lib.IntVal(p.Limit)
}

// PlayTTL returns the maxage and redis TTL
// If more than `maxTTL` in the past, then return `maxTTL`, otherwise return `ttl`
func PlayTTL(to string, ttl, maxTTL time.Duration, endpointType string) (time.Duration, error) {
	if to != "" {
		// TER-2001 date formatted before calling PlayTTL
		dateformat := dates.DateTimeFormatMAPIURLParam

		toTime, err := time.Parse(dateformat, to)
		if err != nil {
			return maxTTL, err
		}

		diff := toTime.Unix() - time.Now().Unix()
		if diff < 0 && math.Abs(float64(diff)) > maxTTL.Seconds() {
			ttl = maxTTL
		}
	}

	return ttl, nil
}

// PlayTTLSecond returns the maxage and redis TTL by second
// If more than `maxTTL` in the past, then return `maxTTL`, otherwise return `ttl`
func PlayTTLSecond(to string, ttl, maxTTL int, endpointType string) (int, error) {
	value, err := PlayTTL(to, time.Duration(ttl)*time.Second, time.Duration(maxTTL)*time.Second, endpointType)
	return int(value / time.Second), err
}

type TrackSummary struct {
	Artist     *string                 `json:"artist,omitempty"`
	Title      *string                 `json:"title,omitempty"`
	Properties *TrackSummaryProperties `json:"properties,omitempty"`
	Links      *TrackSummaryLinks      `json:"links,omitempty"`
}

type TrackSummaryProperties struct {
	Performers *string `json:"performers,omitempty"`
	Label      *string `json:"label,omitempty"`
	Year       *string `json:"year,omitempty"`
	Countdown  *string `json:"countdown,omitempty"`
}

type TrackSummaryLinks struct {
	Youtube   *string `json:"Youtube,omitempty"`
	Spotify   *string `json:"Spotify,omitempty"`
	Apple     *string `json:"Apple,omitempty"`
	Unearthed *string `json:"Unearthed,omitempty"`
}
