package mapi

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEndpoint(t *testing.T) {
	t.Run("Should return correct endpoint for ArtistEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ArtistEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/artists/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for PlayEntity", func(t *testing.T) {
		endpoint, err := Endpoint(PlayEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/plays/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for RecordingEntity", func(t *testing.T) {
		endpoint, err := Endpoint(RecordingEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/recordings/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for ReleaseEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ReleaseEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/releases/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for ServiceEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ServiceEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/services/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return error endpoint for other entieies", func(t *testing.T) {
		endpoint, err := Endpoint(LiveEntity, "maQ6DdxQDq")
		assert.Equal(t, errors.New("Invalid entity: live"), err)
		assert.Empty(t, endpoint)
		endpoint, err = Endpoint("invalid", "maQ6DdxQDq")
		assert.Equal(t, errors.New("Invalid entity: invalid"), err)
		assert.Empty(t, endpoint)
	})
}

func TestLiveEndpoint(t *testing.T) {
	t.Run("Should return correct endpoint for LiveEntity", func(t *testing.T) {
		endpoint := LiveEndpoint("triplej")
		assert.Equal(t, "/plays/triplej/now.json", endpoint)
	})
}

func TestSearchEndpoint(t *testing.T) {
	t.Run("Should return correct endpoint for ArtistEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(ArtistEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/artists/search.json", endpoint)
	})
	t.Run("Should return correct endpoint for PlayEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(PlayEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/plays/search.json", endpoint)
	})
	t.Run("Should return error for RecordingEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(RecordingEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/recordings/plays.json", endpoint)
	})
	t.Run("Should return error for ReleaseEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(ReleaseEntity)
		assert.Error(t, err)
		assert.Equal(t, errors.New("Invalid entity to get endpoint: release"), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return correct endpoint for ServiceEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(ServiceEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/services.json", endpoint)
	})
	t.Run("Should return error endpoint for other entieies", func(t *testing.T) {
		endpoint, err := SearchEndpoint(LiveEntity)
		assert.Equal(t, errors.New("Invalid entity to get endpoint: live"), err)
		assert.Empty(t, endpoint)
		endpoint, err = SearchEndpoint("invalid")
		assert.Equal(t, errors.New("Invalid entity to get endpoint: invalid"), err)
		assert.Empty(t, endpoint)
	})
}
