package mapi

type Processor interface {
	Process()
	IsMapi()
	ContentID() string
	ContentType() string
	ContentSource() string
	URI() string
}

// Model returns the mapped Model to use for Mapi
func Model(entity Entity) Processor {
	switch entity {
	case PlayEntity:
		return &Play{}
	case RecordingEntity:
		return &Recording{}
	case ReleaseEntity:
		return &Release{}
	case ArtistEntity:
		return &Artist{}
	case ServiceEntity:
		return &Service{}
	default:
		return nil
	}
}
