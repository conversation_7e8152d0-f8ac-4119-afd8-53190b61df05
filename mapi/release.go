package mapi

type Release struct {
	Object
	ArtistList     []*Artist    `json:"ArtistList,omitempty"` // Added from V1
	Artists        []*Artist    `json:"artists,omitempty"`
	Artwork        []*Artwork   `json:"artwork,omitempty"`
	Format         string       `json:"format,omitempty"`
	Links          []*Link      `json:"links,omitempty"`
	RecordLabel    string       `json:"record_label,omitempty"`
	Recordings     []*Recording `json:"recordings,omitempty"`
	ReleaseAlbumID string       `json:"release_album_id,omitempty"`
	ReleaseYear    string       `json:"release_year,omitempty"`
	Title          string       `json:"title,omitempty"`
	IsPrimary      *bool        `json:"is_primary,omitempty"`
}

func (r *Release) Process() {}
func (Release) IsMapi()     {}
