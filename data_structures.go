package lib

type stringSet struct {
	set map[string]struct{}
}

// StringSet is a set holding strings
type StringSet interface {
	Exists(value string) bool
	Add(set StringSet) StringSet
	Substract(set StringSet) StringSet
	ForEach(f func(string) error) error
	Len() int
}

// MutableStringSet is a set holding strings and mutable
type MutableStringSet interface {
	StringSet
	Set(value string)
	Delete(value string)
}

// NewStringSet inits a string set with string slice
func NewStringSet(values ...string) StringSet {
	set := &stringSet{
		set: make(map[string]struct{}),
	}
	for _, value := range values {
		set.set[value] = struct{}{}
	}
	return set
}

// NewMutableStringSet inits a string set with string slice
func NewMutableStringSet(values ...string) MutableStringSet {
	set := &stringSet{
		set: make(map[string]struct{}),
	}
	for _, value := range values {
		set.set[value] = struct{}{}
	}
	return set
}

// Set valued to specified string set
func (s stringSet) Set(value string) {
	s.set[value] = struct{}{}
}

// Exists checks if value exist in the StringSet
func (s stringSet) Exists(value string) bool {
	_, ok := s.set[value]
	return ok
}

// Delete remove a value from StringSet
func (s stringSet) Delete(value string) {
	delete(s.set, value)
}

// Add returns a set of original set plus the items in given set via parameter
func (s stringSet) Add(set StringSet) StringSet {
	sub := &stringSet{
		set: make(map[string]struct{}),
	}
	for k := range s.set {
		sub.Set(k)
	}
	set.ForEach(func(k string) error {
		sub.Set(k)
		return nil
	})
	return sub
}

// Substract returns a clone of original set excludes the items in given set in parameter
func (s stringSet) Substract(set StringSet) StringSet {
	sub := &stringSet{
		set: make(map[string]struct{}),
	}
	for k := range s.set {
		if set.Exists(k) {
			continue
		}
		sub.Set(k)
	}
	return sub
}

// Substract returns a clone of original set excludes the items in given set in parameter
func (s stringSet) ForEach(f func(string) error) error {
	for k := range s.set {
		err := f(k)
		if err != nil {
			return err
		}
	}
	return nil
}

// Substract returns a clone of original set excludes the items in given set in parameter
func (s stringSet) Len() int {
	return len(s.set)
}
