package lib

import (
	"regexp"
)

var isAlphanumericDash = regexp.MustCompile(`^[A-Za-z0-9-]+$`).MatchString
var isNumber = regexp.MustCompile(`^[0-9]+$`).MatchString

//AlphanumericOrHyphen will return false if the string contains anything other than a dash or alpha numeric character
func AlphanumericOrHyphen(str string) bool {
	if !isAlphanumericDash(str) {
		return false
	}

	return true
}

//Numeric will return false if the string contains anything other than numeric character
func Numeric(str string) bool {
	if !isNumber(str) {
		return false
	}

	return true
}
