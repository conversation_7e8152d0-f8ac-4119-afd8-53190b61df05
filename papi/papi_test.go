package papi

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEndpoint(t *testing.T) {
	t.Run("Should return correct endpoint for BrandEntity", func(t *testing.T) {
		endpoint, err := Endpoint(BrandEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/brands/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for CategoryEntity", func(t *testing.T) {
		endpoint, err := Endpoint(CategoryEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/categories/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for ImageEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ImageEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/images/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for ProgramItemEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ProgramItemEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/programitems/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for ProgramEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ProgramEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/programs/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for OndemanEntity", func(t *testing.T) {
		endpoint, err := Endpoint(OndemanEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/on_demand/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for PersonEntity", func(t *testing.T) {
		endpoint, err := Endpoint(PersonEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/people/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for PublicationEventEntity", func(t *testing.T) {
		endpoint, err := Endpoint(PublicationEventEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/publications/maQ6DdxQDq/event.json", endpoint)
	})
	t.Run("Should return correct endpoint for SegmentEntity", func(t *testing.T) {
		endpoint, err := Endpoint(SegmentEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/segments/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for SeriesEntity", func(t *testing.T) {
		endpoint, err := Endpoint(SeriesEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/series/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for ServiceEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ServiceEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/services/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for SubjectEntity", func(t *testing.T) {
		endpoint, err := Endpoint(SubjectEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/subject/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return correct endpoint for WebpageEntity", func(t *testing.T) {
		endpoint, err := Endpoint(WebpageEntity, "maQ6DdxQDq")
		assert.NoError(t, err)
		assert.Equal(t, "/webpages/maQ6DdxQDq.json", endpoint)
	})
	t.Run("Should return error endpoint for other entieies", func(t *testing.T) {
		endpoint, err := Endpoint("invalid", "maQ6DdxQDq")
		assert.Equal(t, errors.New("Invalid entity: invalid"), err)
		assert.Empty(t, endpoint)
	})
}

func TestLiveEndpoint(t *testing.T) {
	t.Run("Should return correct endpoint for LiveEntity", func(t *testing.T) {
		endpoint := LiveEndpoint("triplej")
		assert.Equal(t, "/programitems/triplej/live.json", endpoint)
	})
}

func TestSearchEndpoint(t *testing.T) {
	t.Run("Should return error for BrandEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(BrandEntity)
		assert.Equal(t, errors.New("Invalid entity: brand"), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return correct endpoint for CategoryEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(CategoryEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/categories.json", endpoint)
	})
	t.Run("Should return correct endpoint for ImageEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(ImageEntity)
		assert.Equal(t, errors.New("Invalid entity: "+ImageEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return correct endpoint for ProgramItemEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(ProgramItemEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/programitems/search.json", endpoint)
	})
	t.Run("Should return correct endpoint for ProgramEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(ProgramEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/programs.json", endpoint)
	})
	t.Run("Should return error for OndemanEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(OndemanEntity)
		assert.Equal(t, errors.New("Invalid entity: "+OndemanEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return error for PersonEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(PersonEntity)
		assert.Equal(t, errors.New("Invalid entity: "+PersonEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return error for PublicationEventEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(PublicationEventEntity)
		assert.Equal(t, errors.New("Invalid entity: "+PublicationEventEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return error for SegmentEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(SegmentEntity)
		assert.Equal(t, errors.New("Invalid entity: "+SegmentEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return error for SeriesEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(SeriesEntity)
		assert.Equal(t, errors.New("Invalid entity: "+SeriesEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return error for ServiceEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(ServiceEntity)
		assert.NoError(t, err)
		assert.Equal(t, "/services.json", endpoint)
	})
	t.Run("Should return error for SubjectEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(SubjectEntity)
		assert.Equal(t, errors.New("Invalid entity: "+SubjectEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return error for WebpageEntity", func(t *testing.T) {
		endpoint, err := SearchEndpoint(WebpageEntity)
		assert.Equal(t, errors.New("Invalid entity: "+WebpageEntity), err)
		assert.Empty(t, endpoint)
	})
	t.Run("Should return error endpoint for other entieies", func(t *testing.T) {
		endpoint, err := SearchEndpoint("invalid")
		assert.Equal(t, errors.New("Invalid entity: invalid"), err)
		assert.Empty(t, endpoint)
	})
}
