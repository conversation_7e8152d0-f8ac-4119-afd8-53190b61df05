package papi

// ProgramItems contains the return result from a paginated list of ProgramItem types
type ProgramItems struct {
	Count  *int           `json:"count,omitempty"`
	Items  []*ProgramItem `json:"items,omitempty"`
	Offset *int           `json:"offset,omitempty"`
	Total  *int           `json:"total,omitempty"`
}

// ProgramItem contains the result from a Services call to PAPI
type ProgramItem struct {
	Object
	Categories              []*Category        `json:"categories,omitempty"`     // not in v1
	ChildSegments           []*Segment         `json:"child_segments,omitempty"` // not in v1
	CreatingBrand           *Brand             `json:"creating_brand,omitempty"`
	CreatingService         *Service           `json:"creating_service,omitempty"`
	ExcludeFromIndex        *bool              `json:"exclude_from_index,omitempty"`                                                                                          // not in v1
	LastPublishedDate       string             `json:"last_published_date,omitempty" terminus_rest:"date_reformat(papi,rest,UTC)" terminus_gql:"date_reformat(papi,std,UTC)"` // not in v1
	Live                    []*Live            `json:"live,omitempty"`
	MediumSynopsis          string             `json:"medium_synopsis,omitempty"` // not in v1
	MiniSynopsis            string             `json:"mini_synopsis,omitempty"`
	Ondemand                []*DownloadVersion `json:"ondemand,omitempty"`
	Presenters              []*Person          `json:"presenters,omitempty"`
	PrimaryImage            *Image             `json:"primary_image,omitempty"` // from v1
	PrimaryPublicationEvent *Publication       `json:"primary_publication_event,omitempty"`
	PrimarySMS              string             `json:"primary_sms,omitempty"`
	PrimaryTelephone        string             `json:"primary_telephone,omitempty"`
	PrimaryWebpage          *Webpage           `json:"primary_webpage,omitempty"` // not in v1
	Program                 *Program           `json:"program,omitempty"`
	Properties              []*Property        `json:"properties,omitempty"`
	Series                  *Series            `json:"series,omitempty"`
	ServiceID               string             `json:"service_id,omitempty"` // from v1
	ShortSynopsis           string             `json:"short_synopsis,omitempty"`
	Title                   string             `json:"title,omitempty"`
}

func (ProgramItem) IsPapi()                   {}
func (ProgramItem) IsCoremediaAudioExternal() {}

// ProgramItemsSearch contains the return result from a paginated list of ProgramItem types
// from v1
type ProgramItemsSearch struct {
	Count *int `json:"count,omitempty"`
	Items []*struct {
		ARID string `json:"arid,omitempty"`
	} `json:"items,omitempty"`
	Meta   Meta `json:"-"`
	Offset *int `json:"offset,omitempty"`
	Total  *int `json:"total,omitempty"`
}

// Process converts data to Terminus required formats
func (pi *ProgramItem) Process() {}

// Process converts data to Terminus required formats
func (pis *ProgramItems) Process() {}
