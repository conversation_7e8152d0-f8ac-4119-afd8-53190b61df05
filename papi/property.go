package papi

type Property struct {
	Type        string                   `json:"type,omitempty"`
	User        []string                 `json:"user,omitempty"`
	Value       *bool                    `json:"value,omitempty"`
	CustomValue map[string]string        `json:"custom_value,omitempty"`
	Active      map[string]*ValidityDate `json:"active,omitempty"`
}

type ValidityDate struct {
	StartUtc string `json:"startUtc,omitempty" terminus_rest:"date_reformat(papi,rest,UTC)" terminus_gql:"date_reformat(papi,std,UTC)"`
	EndUtc   string `json:"endUtc,omitempty" terminus_rest:"date_reformat(papi,rest,UTC)" terminus_gql:"date_reformat(papi,std,UTC)"`
}
