package sqs

import (
	"log"
	"testing"
)

func TestWrap(t *testing.T) {
	msg := []byte(`{"SignatureVersion": "1", "Timestamp": "1970-01-01T00:00:00.000Z", "Signature": "EXAMPLE", "SigningCertUrl": "EXAMPLE", "MessageId": "95df01b4-ee98-5cb9-9903-4c221d41eb5e", "Message": "{ \"messageid\": \"34cdaca6-cdff-11e5-ab30-625662870761\", \"contentid\": \"110\", \"contentsource\": \"coremedia\", \"contenttype\": \"article\", \"contentversion\": 62, \"action\": \"create\" }", "Type": "Notification", "UnsubscribeUrl": "EXAMPLE", "TopicArn": "arn:aws:sns:us-east-1:111122223333:ExampleTopic", "Subject": "example subject"}`)
	data := WrapMessage(msg)
	d, _ := json.Marshal(data)
	log.Println(string(d))
}
