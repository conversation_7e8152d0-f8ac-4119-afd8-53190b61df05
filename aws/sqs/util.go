package sqs

import (
	"github.com/aws/aws-lambda-go/events"
	jsoniter "github.com/json-iterator/go"
)

var json = jsoniter.ConfigDefault

// WrapMessage is wrap data into a sqs message.
func WrapMessage(data interface{}) events.SQSMessage {
	content, ok := data.([]byte)
	if !ok {
		content, _ = json.Marshal(data)
	}
	msg := events.SQSMessage{
		Body: string(content),
	}
	return msg
}

// WrapEvent is wrap data into a sqs event.
func WrapEvent(data interface{}) events.SQSEvent {
	msg := WrapMessage(data)
	evt := events.SQSEvent{
		Records: []events.SQSMessage{
			msg,
		},
	}
	return evt
}
