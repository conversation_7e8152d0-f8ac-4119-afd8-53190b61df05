package ssm

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/ssm"
)

type Client struct {
	ssm *ssm.Client
}

func New(cfg *aws.Config) *Client {
	return &Client{
		ssm: ssm.NewFromConfig(*cfg),
	}
}

func (c *Client) GetParamValue(name string, decrypt bool) (string, error) {
	output, err := c.ssm.GetParameter(context.TODO(), &ssm.GetParameterInput{
		Name:           aws.String(name),
		WithDecryption: aws.Bool(decrypt),
	})
	if err != nil {
		return "", err
	}
	v := output.Parameter.Value
	if v != nil {
		return *v, nil
	}
	return "", nil
}
