package lambda

import (
	"context"
	"time"

	"github.com/aws/aws-lambda-go/lambdacontext"
)

// Context is holding context of current lambda
type Context struct {
	ctx context.Context
	lc  *lambdacontext.LambdaContext
}

// FromContext is generate Context from context.Context instance
func FromContext(ctx context.Context) *Context {
	lc, _ := lambdacontext.FromContext(ctx)
	c := &Context{
		ctx: ctx,
		lc:  lc,
	}
	return c
}

// Deadline of lamda execution
func (c *Context) Deadline() (time.Time, bool) {
	return c.ctx.Deadline()
}

// TimeRemain of lamda execution
func (c *Context) TimeRemain() time.Duration {
	deadline, _ := c.Deadline()
	deadline = deadline.Add(-100 * time.Millisecond)
	return deadline.Sub(time.Now())
}

// Done delegates inner context Done method
func (c *Context) Done() <-chan struct{} {
	return c.ctx.Done()
}

// Err delegates inner context Err method
func (c *Context) Err() error {
	return c.ctx.Err()
}

// FunctionName of the lambda
func (c *Context) FunctionName() string {
	return lambdacontext.FunctionName
}

// Version of the lambda
func (c *Context) Version() string {
	return lambdacontext.FunctionVersion
}

// MemoryLimitInMB of the lambda
func (c *Context) MemoryLimitInMB() int {
	return lambdacontext.MemoryLimitInMB
}

// LogGroupName of the lambda
func (c *Context) LogGroupName() string {
	return lambdacontext.LogGroupName
}

// LogStreamName of the lambda
func (c *Context) LogStreamName() string {
	return lambdacontext.LogStreamName
}

// AwsRequestID of the lambda
func (c *Context) AwsRequestID() string {
	return c.lc.AwsRequestID
}

// Value is for get context value by key
func (c *Context) Value(key interface{}) interface{} {
	return c.ctx.Value(key)
}

// SetValue is for set context value
func (c *Context) SetValue(key, value interface{}) {
	c.ctx = context.WithValue(c.ctx, key, value)
}
