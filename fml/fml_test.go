package fml

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"golang.org/x/net/html"
	"golang.org/x/net/html/atom"
)

func TestFMLString(t *testing.T) {
	t.Run("should return emtpy string without error", func(t *testing.T) {
		var n *FML
		str, err := n.String()
		assert.NoError(t, err)
		assert.Empty(t, str)
	})

	t.Run("should return json string", func(t *testing.T) {
		htmlNode := &html.Node{
			Type: html.TextNode,
			Data: "test",
		}
		n := new(FML)
		result := n.traverse(htmlNode, FmlMeta{})

		str, err := result.String()
		assert.NoError(t, err)
		assert.Equal(t, `{"type":"text","content":"test"}`, str)
	})
}

func TestFMLSingleLevelTraverse(t *testing.T) {
	t.Run("Should set correct for TextNode", func(t *testing.T) {
		htmlNode := &html.Node{
			Type: html.TextNode,
			Data: "test",
		}
		n := new(FML)
		result := n.traverse(htmlNode, FmlMeta{})
		assert.Equal(t, "text", result.Type)
		assert.Equal(t, htmlNode.Data, result.Content)
	})
	t.Run("Should set correct for ElementNode", func(t *testing.T) {
		htmlNode := &html.Node{
			Type: html.ElementNode,
			Data: "input",
			Attr: []html.Attribute{
				{
					Namespace: "n",
					Key:       "dummy",
					Val:       "test data",
				},
				{
					Key: "type",
					Val: "button",
				},
			},
		}
		n := new(FML)
		result := n.traverse(htmlNode, FmlMeta{})
		assert.Equal(t, "element", result.Type)
		assert.Equal(t, "input", result.TagName)
		assert.Equal(t, map[string]string{
			"n:dummy": "test data",
			"type":    "button",
		}, result.Parameters)
	})
}

func TestFMLMultipleLevelTraverse(t *testing.T) {
	subNode := &html.Node{
		Type:     html.ElementNode,
		DataAtom: atom.A,
		Data:     "span",
		Attr: []html.Attribute{
			{
				Key: "ref",
				Val: "test",
			},
		},
		FirstChild: &html.Node{
			Type: html.TextNode,
			Data: "test ref node",
		},
	}
	refNode := &html.Node{
		Type:       html.ElementNode,
		Data:       "p",
		DataAtom:   atom.P,
		FirstChild: subNode,
		LastChild:  subNode,
	}
	blockQuote := &html.Node{
		Type:     html.ElementNode,
		DataAtom: atom.Blockquote,
		Data:     "span",
		FirstChild: &html.Node{
			Type:     html.TextNode,
			DataAtom: atom.Cite,
			Data:     "test block quote",
		},
		NextSibling: refNode,
	}
	txtNode := &html.Node{
		Type:        html.TextNode,
		Data:        "test",
		NextSibling: blockQuote,
	}
	htmlNode := &html.Node{
		Type:       html.ElementNode,
		Data:       "div",
		FirstChild: txtNode,
	}
	n := new(FML)
	result := n.traverse(htmlNode, FmlMeta{})
	assert.Equal(t, "element", result.Type)
	assert.Equal(t, "div", result.TagName)
	assert.Equal(t, 3, len(result.Children))
	assert.Equal(t, &FML{
		Type:    "text",
		Content: "test",
	}, result.Children[0], "TextNode should match with expected value")

	assert.Equal(t, &FML{
		Type:    "text",
		Content: "test block quote",
	}, result.Children[1], "Block Quote should match with expected value")

	assert.Equal(t, &FML{
		Type:    "element",
		TagName: "span",
		Parameters: map[string]string{
			"ref": "test",
		},
		Children: []*FML{
			{
				Type:    "text",
				Content: "test ref node",
			},
		},
	}, result.Children[2], "Ref Node (Not ticker) should match with expected value")

	tickerNode := new(FML)
	resultTicker := tickerNode.traverse(htmlNode, FmlMeta{
		DocType: DocTypeTicker,
	})
	assert.Equal(t, &FML{
		Type:       "element",
		TagName:    "p",
		Parameters: make(map[string]string),
		Children: []*FML{
			{
				Type:    "element",
				TagName: "span",
				Parameters: map[string]string{
					"ref": "test",
				},
				Children: []*FML{
					{
						Type:    "text",
						Content: "test ref node",
					},
				},
			},
		},
	}, resultTicker.Children[2], "Ref Node (Ticker) should match with expected value")
	t.Run("No ref attr", func(t *testing.T) {
		subNode := &html.Node{
			Type:     html.ElementNode,
			DataAtom: atom.A,
			Data:     "span",
			FirstChild: &html.Node{
				Type: html.TextNode,
				Data: "test ref node",
			},
		}
		refNode := &html.Node{
			Type:       html.ElementNode,
			Data:       "p",
			DataAtom:   atom.P,
			FirstChild: subNode,
			LastChild:  subNode,
		}
		htmlNode := &html.Node{
			Type:       html.ElementNode,
			Data:       "div",
			FirstChild: refNode,
		}
		n := new(FML)
		resultTicker := n.traverse(htmlNode, FmlMeta{})
		assert.Equal(t, 0, len(resultTicker.Children))
	})
}

func TestNormalize(t *testing.T) {
	t.Run("Given an html 'a' node with an xlink:role attribute", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `xlink:role`,
			Val: `anchorvalue`,
		})

		var n = &html.Node{
			Data:     "a",
			DataAtom: atom.A,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "anchor" {
				a = v
			}
		}
		assert.Equal(t, `anchor`, a.Key, "Should contain an anchor tag set to correct value")
		assert.Equal(t, `anchorvalue`, a.Val, "Should contain an anchor tag set to correct value")
	})

	t.Run("Given an html 'a' node with an xlink:show attribute", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `xlink:show`,
			Val: `replace`,
		})

		var n = &html.Node{
			Data:     "a",
			DataAtom: atom.A,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "show" {
				a = v
			}
		}
		assert.Equal(t, `show`, a.Key, "Should contain an show tag set to correct value")
		assert.Equal(t, `replace`, a.Val, "Should contain an show tag set to correct value")
	})

	t.Run("Given an html 'a' node with an xlink:href attribute containing a contentbean", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `xlink:href`,
			Val: `contentbean:123456`,
		})

		var n = &html.Node{
			Data:     "a",
			DataAtom: atom.A,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "ref" {
				a = v
			}
		}
		assert.Equal(t, `ref`, a.Key, "Should contain an anchor tag set to correct value")
		assert.Equal(t, `123456`, a.Val, "Should contain an anchor tag set to correct value")
	})

	t.Run("Given an html 'a' node with an xlink:href attribute containing no contentbean", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `xlink:href`,
			Val: `http://abc.net.au/`,
		})

		var n = &html.Node{
			Data:     "a",
			DataAtom: atom.A,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "href" {
				a = v
			}
		}

		assert.Equal(t, `href`, a.Key, "Should contain an anchor tag set to correct value")
		assert.Equal(t, `http://abc.net.au/`, a.Val, "Should contain an anchor tag set to correct value")
	})

	t.Run("Given an html 'img' node with an xlink:href attribute containing coremedia reference", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `xlink:href`,
			Val: `coremedia:///cap/content/123456`,
		})

		var n = &html.Node{
			Data:     "img",
			DataAtom: atom.Img,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "ref" {
				a = v
			}
		}
		assert.Equal(t, `ref`, a.Key, "Should contain an anchor tag set to correct value")
		assert.Equal(t, `123456`, a.Val, "Should contain an anchor tag set to correct value")
	})

	t.Run("Given an html 'img' node with an xlink:href attribute containing no contentbean", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `xlink:href`,
			Val: `http://abc.net.au/`,
		})

		var n = &html.Node{
			Data:     "img",
			DataAtom: atom.Img,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "href" {
				a = v
			}
		}

		assert.Equal(t, `href`, a.Key, "Should contain an anchor tag set to correct value")
		assert.Equal(t, `http://abc.net.au/`, a.Val, "Should contain an anchor tag set to correct value")
	})

	t.Run("Given an html 'img' node with an xlink:show attribute", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `xlink:show`,
			Val: `replace`,
		})

		var n = &html.Node{
			Data:     "a",
			DataAtom: atom.Img,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "show" {
				a = v
			}
		}

		assert.Equal(t, `show`, a.Key, "Should contain an show tag set to correct value")
		assert.Equal(t, `replace`, a.Val, "Should contain an show tag set to correct value")
	})

	t.Run("Given an html 'img' node with an href attribute", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `href`,
			Val: `http://abc.net.au/`,
		})

		var n = &html.Node{
			Data:     "img",
			DataAtom: atom.Img,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "href" {
				a = v
			}
		}

		assert.Equal(t, `href`, a.Key, "Should contain an anchor tag set to correct value")
		assert.Equal(t, `http://abc.net.au/`, a.Val, "Should contain an anchor tag set to correct value")
	})

	t.Run("Given an html 'img' node with an target attribute", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `target`,
			Val: `http://abc.net.au/`,
		})

		var n = &html.Node{
			Data:     "img",
			DataAtom: atom.Img,
			Attr:     attr,
		}

		Normalize(n)

		var a html.Attribute
		for _, v := range n.Attr {
			if v.Key == "target" {
				a = v
			}
		}

		assert.Equal(t, `target`, a.Key, "Should contain an anchor tag set to correct value")
		assert.Equal(t, `http://abc.net.au/`, a.Val, "Should contain an anchor tag set to correct value")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 1", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-1`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h1`, n.Data, "Should have been converted to a H1 node")
		assert.Equal(t, atom.H1, n.DataAtom, "Should have been converted to a H1 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H1 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 1", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-2 p--heading-1`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 2", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-2`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h2`, n.Data, "Should have been converted to a H2 node")
		assert.Equal(t, atom.H2, n.DataAtom, "Should have been converted to a H2 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H2 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 2", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-1      	  p--heading-2`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 3", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-3`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h3`, n.Data, "Should have been converted to a H3 node")
		assert.Equal(t, atom.H3, n.DataAtom, "Should have been converted to a H3 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H3 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 3", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-1 p--heading-3 bbb`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 4", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-4`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h4`, n.Data, "Should have been converted to a H4 node")
		assert.Equal(t, atom.H4, n.DataAtom, "Should have been converted to a H4 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H4 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 4", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-1 p--heading-4`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 5", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-5`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h5`, n.Data, "Should have been converted to a H5 node")
		assert.Equal(t, atom.H5, n.DataAtom, "Should have been converted to a H5 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H5 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 5", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-1 p--heading-5`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 6", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-6`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h6`, n.Data, "Should have been converted to a H6 node")
		assert.Equal(t, atom.H6, n.DataAtom, "Should have been converted to a H6 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H6 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 6", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-1 p--heading-6`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 7", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-7`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h7`, n.Data, "Should have been converted to a H6 node")
		assert.Equal(t, atom.H6, n.DataAtom, "Should have been converted to a H6 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H6 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 7", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-1 p--heading-7`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a heading level 8", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--heading-8`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `h8`, n.Data, "Should have been converted to a H6 node")
		assert.Equal(t, atom.H6, n.DataAtom, "Should have been converted to a H6 node")
		assert.Nil(t, n.Attr, "Should have been converted to a H6 node")
	})

	t.Run("Given an html 'p' node with multiple classes refering to a heading level 8", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `xxx p--heading-1 p--heading-8`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `p`, n.Data, "Should have been converted to a P node")
		assert.Equal(t, atom.P, n.DataAtom, "Should have been converted to a P node")
		assert.Nil(t, n.Attr, "Should have been converted to a P node")
	})

	t.Run("Given an html 'p' node with a class refering to a pre element", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--pre`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `pre`, n.Data, "Should have been converted to a pre node")
		assert.Equal(t, atom.Pre, n.DataAtom, "Should have been converted to a pre node")
		assert.Nil(t, n.Attr, "Should have been converted to a pre node")
	})

	t.Run("Given an html 'p' node with a class refering to a cite element", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `p--pullquote-byline`,
		})

		var n = &html.Node{
			Data:     "p",
			DataAtom: atom.P,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `cite`, n.Data, "Should have been converted to a cite node")
		assert.Equal(t, atom.Cite, n.DataAtom, "Should have been converted to a cite node")
		assert.Nil(t, n.Attr, "Should have been converted to a cite node")
	})

	t.Run("Given an html 'blockquote' node with a class refering to a pullquote element", func(t *testing.T) {
		var attr []html.Attribute
		attr = append(attr, html.Attribute{
			Key: `class`,
			Val: `quote--pullquote`,
		})

		var n = &html.Node{
			Data:     "blockquote",
			DataAtom: atom.Blockquote,
			Attr:     attr,
		}

		Normalize(n)

		assert.Equal(t, `pullquote`, n.Data, "Should have been converted to a pullquote node (this is not a valid HTML element and so keeps the atomic value of a blockquote)")
		assert.Equal(t, atom.Blockquote, n.DataAtom, "Should have been converted to a pullquote node")
		assert.Empty(t, n.Attr, "Should have been converted to a pullquote node")
	})

}

func TestFMLAlign(t *testing.T) {
	t.Run("Given an attribute value of replace", func(t *testing.T) {
		attr := align(html.Attribute{
			Val: "replace",
		})
		assert.Equal(t, `show`, attr.Key, "Should return an object with a key of show")
		assert.Equal(t, `replace`, attr.Val, "Should return an object with a value of replace")
	})

	t.Run("Given an attribute value of new", func(t *testing.T) {
		attr := align(html.Attribute{
			Val: "new",
		})
		assert.Equal(t, `show`, attr.Key, "Should return an object with a key of show")
		assert.Equal(t, `new`, attr.Val, "Should return an object with a value of new")
	})

	t.Run("Given an attribute value of new", func(t *testing.T) {
		attr := align(html.Attribute{
			Val: "embed",
		})
		assert.Equal(t, `align`, attr.Key, "Should return an object with a key of align")
		assert.Equal(t, `embed`, attr.Val, "Should return an object with a value of embed")
	})

	t.Run("Given an attribute value of other", func(t *testing.T) {
		attr := align(html.Attribute{
			Val: "other",
		})
		assert.Equal(t, `align`, attr.Key, "Should return an object with a key of align")
		assert.Equal(t, `left`, attr.Val, "Should return an object with a value of left")
	})

	t.Run("Given an attribute value of none", func(t *testing.T) {
		attr := align(html.Attribute{
			Val: "none",
		})

		assert.Equal(t, `align`, attr.Key, "Should return an object with a key of align")
		assert.Equal(t, `right`, attr.Val, "Should return an object with a value of right")
	})

	t.Run("Rendering links in various scenerios", func(t *testing.T) {
		meta := FmlMeta{
			DocType: "Article",
		}

		xml := `
<div xmlns="http://www.coremedia.com/2003/richtext-1.0" xmlns:xlink="http://www.w3.org/1999/xlink">
   <p>Hello there</p>
   <p><a xlink:show="new" xlink:href="http://www.abc.net">in the news today</a></p>
</div>`
		fml := FromXMLWithMeta(xml, meta)
		value, _ := fml.String()
		assert.Contains(t, value, `Hello there`, "Should render a single <a> inside a <p> with no other attributes, i.e. <p><a/></p>")
		assert.Contains(t, value, `http://www.abc.net`, "Should render a single <a> inside a <p> with no other attributes, i.e. <p><a/></p>")
		assert.Contains(t, value, `in the news today`, "Should render a single <a> inside a <p> with no other attributes, i.e. <p><a/></p>")

		xml = `
<div xmlns="http://www.coremedia.com/2003/richtext-1.0" xmlns:xlink="http://www.w3.org/1999/xlink">
   <p>Oh hi</p>
   <p><a xlink:show="new" xlink:href="http://www.abc.net">in the news today</a>RANDOM TEXT NODE HERE</p>
</div>`
		fml = FromXMLWithMeta(xml, meta)
		value, _ = fml.String()
		assert.Contains(t, value, `Oh hi`, "Should render a single <a> inside a <p> with no other attributes, i.e. <p><a/></p>")
		assert.Contains(t, value, `http://www.abc.net`, "Should render a single <a> inside a <p> with no other attributes, i.e. <p><a/></p>")
		assert.Contains(t, value, `in the news today`, "Should render a single <a> inside a <p> with no other attributes, i.e. <p><a/></p>")

		xml = `
<div xmlns="http://www.coremedia.com/2003/richtext-1.0" xmlns:xlink="http://www.w3.org/1999/xlink">
   <p>heyhey</p>
   <ul><li><a xlink:show="new" xlink:href="http://www.abc.net">in the news today</a></li></ul>
</div>`
		fml = FromXMLWithMeta(xml, meta)
		value, _ = fml.String()
		assert.Contains(t, value, `heyhey`, "Should render a single <a> if it is a list item, i.e. <ul><li><a/></li></ul>")
		assert.Contains(t, value, `http://www.abc.net`, "Should render a single <a> if it is a list item, i.e. <ul><li><a/></li></ul>")
		assert.Contains(t, value, `in the news today`, "Should render a single <a> if it is a list item, i.e. <ul><li><a/></li></ul>")
	})
}
