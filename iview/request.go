package iview

import (
	"fmt"
	"net/url"
	"strings"

	"github.com/google/go-querystring/query"
	"github.com/spf13/viper"
	"stash.abc-dev.net.au/ter/lib"
	"stash.abc-dev.net.au/ter/lib/cache"
)

const (
	// ActionCreate is for create action
	ActionCreate = "create"
	// ActionUpdate is for update action
	ActionUpdate = "update"
	// ActionDelete is for delete action
	ActionDelete = "delete"
	// ActionHDelete is for hard delete action
	ActionHDelete = "hdelete"
)

const (
	IVIEW_CONTENT_REQUEST_ARN = "IVIEW_CONTENT_REQUEST_ARN"
)

const (
	DefaultLimitCollectionItems = 20
	DefaultLimitCollections     = 10
)

// ContentSource returns the uppercase of passing string or default value
func ContentSource(src string) string {
	if src == "" {
		return strings.ToUpper(lib.IView)
	}

	return strings.ToUpper(src)
}

type ContentMeta struct {
	ShowID             string `json:"showID,omitempty"`
	Slug               string `json:"slug,omitempty"`
	SeriesNumber       string `json:"seriesNumber,omitempty"`
	EpisodeHouseNumber string `json:"episodeHouseNumber,omitempty"`
}

type ContentLinks struct {
	Self struct {
		Href string `json:"href"`
	} `json:"self"`
	Deeplink struct {
		Href string `json:"href"`
	} `json:"deeplink"`
}

// ContentRequest describe the struct of iView SQS request
type ContentRequest struct {
	Action      string        `json:"action"`
	ContentType Entity        `json:"contenttype"`
	ContentID   string        `json:"contentid"`
	Source      string        `json:"contentsource"`
	Params      ContentParams `json:"params,omitempty"`
	Meta        ContentMeta   `json:"metadata,omitempty"`
	Links       ContentLinks  `json:"links,omitempty"`
	Rid         string        `json:"rid"`
	FromNotice  bool          `json:"fromNotice,omitempty"`
	Version     int           `json:"contentversion"`
}

// ContentParams defines the extra parameters of MAPI content request
type ContentParams struct {
	Embed   string `json:"embed,omitempty" url:"embed,omitempty"`     // Used for collection API
	Include string `json:"include,omitempty" url:"include,omitempty"` // Used on all endpoints to get new fields and reformat date to ISO8601
}

func (p ContentParams) URLValues() (url.Values, error) {
	return query.Values(p)
}

func NewContentRequest(action lib.Action, contentType Entity, contentID, reqID string, meta ContentMeta, links ContentLinks, params ContentParams) ContentRequest {
	// for iview show, copy the id to meta.ShowID if not exist
	if contentType == ShowEntity && meta.ShowID == "" {
		meta.ShowID = contentID
	}

	cr := ContentRequest{
		Action:      action,
		ContentType: contentType,
		ContentID:   contentID,
		Meta:        meta,
		Links:       links,
		Source:      strings.ToUpper(lib.IView),
		Version:     1,
		Params:      params,
		Rid:         reqID,
	}

	return prepareRequest(cr)
}

// CacheKey returns key for caching. format is mapi::lower(entity)::{arid}
func (r ContentRequest) CacheKey() string {
	// params, _ := r.Params.URLValues() //we are not including url params as all are backend only, customers will never specify.
	return cache.CacheKey(lib.IView, []string{string(r.DocType()), cacheKeyMeta(r.DocType(), r.ContentID, r.Meta)}, nil)
}

func (r ContentRequest) DocType() Entity {
	return strings.ToLower(r.ContentType)
}

func (r ContentRequest) DocID() string {
	return r.ContentID
}

func (r ContentRequest) ContentMeta() ContentMeta {
	return r.Meta
}

// IsFromNotice determines if this message is from an sqs message or not.
func (r ContentRequest) IsFromNotice() bool {
	return r.FromNotice
}

// GetAction returns the action of request
func (r ContentRequest) GetAction() (lib.Action, error) {
	switch strings.ToLower(r.Action) {
	case ActionCreate:
		return lib.ActionCreate, nil
	case ActionUpdate:
		return lib.ActionUpdate, nil
	case ActionDelete:
		return lib.ActionDelete, nil
	case ActionHDelete:
		return lib.ActionHDelete, nil
	default:
		return "", fmt.Errorf("invalid action: %s", r.Action)
	}
}

func (r ContentRequest) FunctionName() string {
	return viper.GetString("IVIEW_CONTENT_REQUEST_ARN")
}

// URI returns canonical URI base on request info
func (r ContentRequest) URI() string {
	return lib.URI(lib.IView, r.DocType(), r.ContentID) //@Todo need to fix this to include all possible urls. Series breaks.
}

// Path returns url path of the request
func (r ContentRequest) Path() (string, error) {
	id := r.DocID()
	meta := r.ContentMeta()

	path, err := Endpoint(r.DocType(), id, meta)
	if err != nil {
		return "", err
	}

	params, _ := r.Params.URLValues()

	return lib.JoinPath(path, params.Encode())
}

// ContentSource returns the source that the request is asking
func (r ContentRequest) ContentSource() string {
	return ContentSource(r.Source)
}

// RequestID returns the random 8 character ID generated by Enrichment. It will return "sqs" if generated by notification
func (r ContentRequest) RequestID() string {
	return r.Rid
}

// RequestType returns the type of request: Content, Search, Live, etc.
func (r ContentRequest) RequestType() lib.RequestType {
	return lib.ContentRequest
}

// prepareRequest adds in the mandatory embeds and includes
func prepareRequest(req lib.Request) ContentRequest {
	request := req.(ContentRequest)

	switch request.DocType() {
	case ShowEntity:
		request.Params.Embed = GetEmbeds(ShowEntity)
		request.Params.Include = GetIncludes(ShowEntity)
	case SeriesEntity:
		request.Params.Include = GetIncludes(SeriesEntity)
	case VideoEntity:
		request.Params.Include = GetIncludes(VideoEntity)
	case CollectionEntity:
		request.Params.Include = GetIncludes(CollectionEntity)
	}

	return request
}
