package iview

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEndpoint(t *testing.T) {
	t.Run("VideoEntity", func(t *testing.T) {
		endpoint, err := Endpoint(VideoEntity, "id", ContentMeta{})
		assert.NoError(t, err)
		assert.Equal(t, "/video/id", endpoint, "Should return correct endpoint for VideoEntity")
	})
	t.Run("SeriesEntity", func(t *testing.T) {
		endpoint, err := Endpoint(SeriesEntity, "id", ContentMeta{"", "doctor-who", "1", ""})
		assert.NoError(t, err)
		assert.Equal(t, "/series/doctor-who/1", endpoint, "Should return correct endpoint for SeriesEntity")
	})
	t.Run("ShowEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ShowEntity, "id", ContentMeta{})
		assert.NoError(t, err)
		assert.Equal(t, "/show/id", endpoint, "Should return correct endpoint for ShowEntity")
	})
	t.Run("ShowVideoEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ShowVideoEntity, "ABCDEFG", ContentMeta{"123456", "doctor-who", "1", ""})
		assert.NoError(t, err)
		assert.Equal(t, "/show/doctor-who/video/ABCDEFG", endpoint, "Should return correct endpoint for ShowVideoEntity")
	})
	t.Run("ShowSeriesVideoEntity", func(t *testing.T) {
		endpoint, err := Endpoint(ShowSeriesVideoEntity, "ABCDEFG", ContentMeta{"", "doctor-who", "1", "12345"})
		assert.NoError(t, err)
		assert.Equal(t, "/show/doctor-who/series/1/video/ABCDEFG", endpoint, "Should return correct endpoint for ShowSeriesVideoEntity")
	})
	t.Run("Other entities", func(t *testing.T) {
		endpoint, err := Endpoint("invalid", "id", ContentMeta{})
		assert.Error(t, err)
		assert.Equal(t, errors.New("Invalid entity: invalid"), err, "Should return error endpoint for other entieies")
		assert.Equal(t, "", endpoint, "Should return empty endpoint for other entieies")
	})
}

type AmbiguousArrayInside[T any] struct {
	Name  string            `json:"name,omitempty"`
	Array AmbiguousArray[T] `json:"array,omitempty"`
	Val   int               `json:"val,omitempty"`
}

func TestAmbiguousArray(t *testing.T) {
	t.Run("single simple item", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[string]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": "test string",
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 1, "should only contains 1 item")
		assert.Equal(t, "test string", testStruct.Array[0])

		testStruct1 := &AmbiguousArrayInside[int]{}
		testJSON = []byte(`{
			"name":"test name",
			"array":1,
			"val": 1
		}`)
		err = json.Unmarshal(testJSON, testStruct1)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct1.Name)
		assert.Equal(t, 1, testStruct1.Val)
		assert.Len(t, testStruct1.Array, 1, "should only contains 1 item")
		assert.Equal(t, 1, testStruct1.Array[0])
	})
	t.Run("single pointer item", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[*string]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": "test string",
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 1, "should only contains 1 item")
		assert.Equal(t, "test string", *testStruct.Array[0])

		testStruct1 := &AmbiguousArrayInside[*int]{}
		testJSON = []byte(`{
			"name":"test name",
			"array":1,
			"val": 1
		}`)
		err = json.Unmarshal(testJSON, testStruct1)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct1.Name)
		assert.Equal(t, 1, testStruct1.Val)
		assert.Len(t, testStruct1.Array, 1, "should only contains 1 item")
		assert.Equal(t, 1, *testStruct1.Array[0])
	})
	t.Run("multiple string items", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[string]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": 
			[
				"test string 1",
				"test string 2"
			],
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 2, "should only contains 2 items")
		assert.Equal(t, "test string 1", testStruct.Array[0])
		assert.Equal(t, "test string 2", testStruct.Array[1])
	})
	t.Run("single string item in array", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[string]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": [
				"test string"
			],
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 1, "should only contains 1 item")
		assert.Equal(t, "test string", testStruct.Array[0])
	})
	t.Run("single struct item", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": {
				"name": "test image",
				"url": "test url"
			},
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 1, "should only contains 1 item")
		assert.Equal(t, "test url", testStruct.Array[0].URL)
		assert.Equal(t, "test image", testStruct.Array[0].Name)
	})
	t.Run("single struct ref item", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[*Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": {
				"name": "test image",
				"url": "test url"
			},
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 1, "should only contains 1 item")
		assert.Equal(t, "test url", testStruct.Array[0].URL)
		assert.Equal(t, "test image", testStruct.Array[0].Name)
	})
	t.Run("multiple struct items", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array":     [
				{
					"name": "test image 1",
					"url": "test url 1"
				},
				{
					"name": "test image 2",
					"url": "test url 2"
				}
			],
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 2, "should only contains 2 items")
		assert.Equal(t, "test url 1", testStruct.Array[0].URL)
		assert.Equal(t, "test image 1", testStruct.Array[0].Name)
		assert.Equal(t, "test url 2", testStruct.Array[1].URL)
		assert.Equal(t, "test image 2", testStruct.Array[1].Name)
	})
	t.Run("multiple struct ref items", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[*Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": [
				{
					"name": "test image 1",
					"url": "test url 1"
				},
				{
					"name": "test image 2",
					"url": "test url 2"
				}
			],
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 2, "should only contains 2 items")
		assert.Equal(t, "test url 1", testStruct.Array[0].URL)
		assert.Equal(t, "test image 1", testStruct.Array[0].Name)
		assert.Equal(t, "test url 2", testStruct.Array[1].URL)
		assert.Equal(t, "test image 2", testStruct.Array[1].Name)
	})
	t.Run("single struct item in array", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": [
				{
					"name": "test image",
					"url": "test url"
				}
			],
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 1, "should only contains 1 item")
		assert.Equal(t, "test url", testStruct.Array[0].URL)
		assert.Equal(t, "test image", testStruct.Array[0].Name)
	})
	t.Run("single struct ref item in array", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[*Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": [
				{
					"name": "test image",
					"url": "test url"
				}
			],
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 1, "should only contains 1 item")
		assert.Equal(t, "test url", testStruct.Array[0].URL)
		assert.Equal(t, "test image", testStruct.Array[0].Name)
	})
	t.Run("empty array", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[*Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": [],
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 0, "should only contains 0 item")
	})
	t.Run("null array", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[*Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"array": null,
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 0, "should only contains 0 item")
	})
	t.Run("no array", func(t *testing.T) {
		testStruct := &AmbiguousArrayInside[*Image]{}
		testJSON := []byte(`{
			"name":"test name",
			"val": 1
		}`)
		err := json.Unmarshal(testJSON, testStruct)
		assert.NoError(t, err, "should not have error")
		assert.Equal(t, "test name", testStruct.Name)
		assert.Equal(t, 1, testStruct.Val)
		assert.Len(t, testStruct.Array, 0, "should only contains 0 item")
	})
}
