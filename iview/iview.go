package iview

import (
	"errors"
	"fmt"

	"stash.abc-dev.net.au/ter/lib"
)

// Entity define the type of iView entity
type Entity = string

// defines entities of iView
const (
	CollectionEntity      Entity = "collection"
	SeriesEntity          Entity = "series"
	ShowVideoEntity       Entity = "showvideo"       // deeplink for featured Video
	ShowSeriesVideoEntity Entity = "showseriesvideo" // deeplink for episode Video
	ShowEntity            Entity = "show"
	VideoEntity           Entity = "video"
	CategoryEntity        Entity = "category"
	ChannelEntity         Entity = "channel"
)

const apiVersion = "v3"

// Endpoint returns iView url base on enity and some ids
func Endpoint(entity Entity, id string, meta ContentMeta) (string, error) {
	// we moved ingester into enrichment, and the url has apiversion in enrichment
	switch entity {
	case VideoEntity:
		return fmt.Sprintf("/video/%s", id), nil
	case SeriesEntity:
		if meta.Slug != "" && meta.SeriesNumber != "" {
			return fmt.Sprintf("/series/%s/%s", meta.Slug, meta.SeriesNumber), nil
		}
		// use id when fetching from sub query of graphql, eg. show, it will not have slug and series number
		return fmt.Sprintf("/series/%s", id), nil
	case ShowEntity:
		return fmt.Sprintf("/show/%s", id), nil //@todo what to do about show slug vs id?
	case ShowVideoEntity:
		return fmt.Sprintf("/show/%s/video/%s", meta.Slug, id), nil
	case ShowSeriesVideoEntity:
		return fmt.Sprintf("/show/%s/series/%s/video/%s", meta.Slug, meta.SeriesNumber, id), nil
	case CollectionEntity:
		return fmt.Sprintf("/collection/%s", id), nil
	case CategoryEntity:
		return fmt.Sprintf("/category/%s", id), nil
	case ChannelEntity:
		return fmt.Sprintf("/channel/%s", id), nil
	}

	return "", errors.New("Invalid entity: " + string(entity))
}

// cacheKeyMeta generates the middle part of the cache key based on the meta information.
// This part sits between upstream::entity and params, e.g. iview::video::<meta>::params
func cacheKeyMeta(entity Entity, id string, meta ContentMeta) string {
	switch string(entity) {
	case VideoEntity, CollectionEntity:
		return id
	case SeriesEntity:
		if meta.Slug != "" && meta.SeriesNumber != "" {
			return fmt.Sprintf("%s::%s", meta.Slug, meta.SeriesNumber)
		}
		// when fetching from sub query of graphql, eg. show, it will not have slug and series number
		return id
	case ShowEntity:
		if meta.Slug != "" {
			return meta.Slug
		}
		if meta.ShowID != "" {
			return meta.ShowID
		}
		// if meta is empty, use id
		return id
	case ShowVideoEntity:
		return fmt.Sprintf("%s::%s", meta.Slug, id)
	case ShowSeriesVideoEntity:
		return fmt.Sprintf("%s::%s::%s", meta.Slug, meta.SeriesNumber, id)
	case CategoryEntity, ChannelEntity:
		return id
	}

	return "invalidentity" // shouldn't be possible to ever hit here, as entities are strictly defined
}

type AmbiguousArray[T any] []T

func (a *AmbiguousArray[T]) UnmarshalJSON(b []byte) error {
	if string(b) == "null" {
		*a = nil
		return nil
	}
	if b[0] != '[' {
		item := new(T)
		err := lib.StdJSON.Unmarshal(b, item)
		if err != nil {
			return err
		}
		*a = append(*a, *item)
		return nil
	}
	t := make([]T, 0, 10)
	err := lib.StdJSON.Unmarshal(b, &t)
	if err != nil {
		return err
	}
	*a = append(*a, t...)
	return nil
}

// Convert to actual typed array
func (a AmbiguousArray[T]) Unbox() []T {
	return []T(a)
}

// Convert to actual pointer typed array
func (a AmbiguousArray[T]) UnboxToPointerArray() []*T {
	result := make([]*T, 0, len(a))
	for _, v := range a {
		item := v
		result = append(result, &item)
	}
	return result
}

func (a AmbiguousArray[T]) FilterToPointer(cond func(t T) bool) []*T {
	result := []*T{}
	for _, v := range a {
		if cond(v) {
			item := v
			result = append(result, &item)
		}
	}
	return result
}
