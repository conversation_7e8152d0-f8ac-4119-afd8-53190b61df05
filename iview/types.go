package iview

type Processor interface {
	Process()
}

// Model returns the mapped Model to use for Mapi
func Model(entity Entity) Processor {
	switch entity {
	case ShowEntity:
		return &Show{}
	case SeriesEntity:
		return &Series{}
	case VideoEntity:
		return &Video{}
	case CollectionEntity:
		return &Collection{}
	case ShowVideoEntity:
		return &ShowVideo{}
	case ShowSeriesVideoEntity:
		return &ShowSeriesVideo{}
	case CategoryEntity, ChannelEntity:
		return &Category{}
	default:
		return nil
	}
}
