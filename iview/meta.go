package iview

import "net/url"

//Meta contains response metadata such as cache controls headers
type Meta struct {
	CacheControlHeaders string            `json:"CacheControlHeaders,omitempty"`
	ContentOrigin       string            `json:"ContentOrigin,omitempty"`
	ContentVersion      int               `json:"ContentVersion,omitempty"`
	LastModified        string            `json:"LastModified,omitempty"`
	Links               map[string]string `json:"Links,omitempty"`
	URLParams           url.Values        `json:"URLParams,omitempty"`
	View                string            `json:"View,omitempty"`
}
