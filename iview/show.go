package iview

import (
	"errors"
	"strconv"
	"strings"

	"stash.abc-dev.net.au/ter/lib"
)

type Show struct {
	ID                        int                 `json:"id,omitempty"`
	Slug                      string              `json:"slug,omitempty"`
	Tags                      []string            `json:"tags,omitempty"`
	Type                      string              `json:"type,omitempty"`
	Title                     string              `json:"title,omitempty"`
	Availability              string              `json:"availability,omitempty"`
	Banner                    []Image             `json:"banner,omitempty"`
	BroadcastInfo             string              `json:"broadcastInfo,omitempty"` // Only on some shows
	Buy                       BuyLinks            `json:"buy,omitempty"`           // Only show up when show with buy links
	Captions                  bool                `json:"captions,omitempty"`
	CaptionsOnAkamai          bool                `json:"captionsOnAkamai,omitempty"`
	Classification            string              `json:"classification,omitempty"`
	DisplayDuration           string              `json:"displayDuration,omitempty"`
	DisplayDurationAccessible string              `json:"displayDurationAccessible,omitempty"`
	DisplaySubtitle           string              `json:"displaySubtitle,omitempty"` // not in v1
	DisplayTitle              string              `json:"displayTitle,omitempty"`
	Description               string              `json:"description,omitempty"`
	Duration                  int                 `json:"duration,omitempty"`
	Status                    Status              `json:"status,omitempty"`
	Theme                     string              `json:"theme,omitempty"`
	EpisodeCount              int                 `json:"episodeCount,omitempty"`
	RelatedLinks              []Link              `json:"relatedLinks,omitempty"`
	ShareURL                  string              `json:"shareUrl,omitempty"`
	SocialLinks               SocialLinks         `json:"socialLinks,omitempty"`
	Entity                    string              `json:"_entity,omitempty"`
	Links                     Links               `json:"_links,omitempty"`
	Misspellings              []string            `json:"misspellings,omitempty"`
	Phrases                   map[string][]string `json:"phrases,omitempty"`
	Playable                  bool                `json:"playable,omitempty"`
	ProductionYear            string              `json:"productionYear,omitempty"`     // Only on movies
	UnavailableMessage        string              `json:"unavailableMessage,omitempty"` // Only appear if a show is not avaialble
	Updated                   string              `json:"updated,omitempty"`            // we usually use string for dates... trying out time.Time, lets see how it goes.
	VideoCount                int                 `json:"videoCount,omitempty"`
	Embedded                  struct {
		HighlightVideo *Video            `json:"highlightVideo,omitempty"`
		SelectedSeries *Series           `json:"selectedSeries,omitempty"`
		SeriesList     []*SeriesListItem `json:"seriesList,omitempty"`
	} `json:"_embedded,omitempty"`
	Participants             []Participant `json:"participants,omitempty"`
	Images                   []Image       `json:"images,omitempty"`
	AudioDescriptionsEnabled bool          `json:"audioDescriptionsEnabled,omitempty"`
	AudioDescriptionsInfo    string        `json:"audioDescriptionsInfo,omitempty"`
	ShortSynopsis            string        `json:"shortSynopsis,omitempty"`
	Profiles                 []string      `json:"profiles,omitempty"`
}

// ContentType returns the lowercase of entity
func (s Show) ContentType() string {
	return strings.ToLower(s.Entity)
}

// ContentSource returns the source string
func (s Show) ContentSource() string {
	return lib.IView
}

// URI returns the cannonical uri
func (s Show) URI() string {
	return lib.URI(s.ContentSource(), s.ContentType(), strconv.Itoa(s.ID))
}

// _embedded.highlightVideo
func (s Show) HighlightVideo() *Video {
	return s.Embedded.HighlightVideo
}

// _embedded.selectedSeries
func (s Show) SelectedSeries() *Series {
	return s.Embedded.SelectedSeries
}

// _embedded.seriesList
func (s Show) SeriesList() []*SeriesListItem {
	return s.Embedded.SeriesList
}

func GetShowEndpoint(id string, meta ContentMeta) (string, error) {
	return Endpoint(ShowEntity, id, meta)
}

// ThumbnailImage returns image object can be used as thumbnail for v2 compatibility
func (c Show) ThumbnailImage() (Image, error) {
	for _, image := range c.Images {
		if image.Type != Thumb {
			continue
		}
		if image.Name == SeriesThumbnail {
			return image, nil
		}
	}
	return Image{}, errors.New("can't find image with name " + SeriesThumbnail)
}

func (s *Show) Process() {}

type Trailer struct {
	ID   string `json:"id,omitempty"`
	Href string `json:"href,omitempty"`
}
