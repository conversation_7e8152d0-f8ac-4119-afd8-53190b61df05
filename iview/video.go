package iview

import (
	"errors"
	"strings"

	"stash.abc-dev.net.au/ter/lib"
)

type Video struct {
	Analytics                 Analytics             `json:"analytics,omitempty"`
	Availability              string                `json:"availability,omitempty"`
	Captions                  bool                  `json:"captions,omitempty"`
	CaptionsOnAkamai          bool                  `json:"captionsOnAkamai,omitempty"`
	Channel                   string                `json:"channel,omitempty"`
	ChannelTitle              string                `json:"channelTitle,omitempty"` // from v1
	Classification            string                `json:"classification,omitempty"`
	Description               string                `json:"description,omitempty"`
	DisplayDuration           string                `json:"displayDuration,omitempty"`
	DisplayDurationAccessible string                `json:"displayDurationAccessible,omitempty"`
	DisplaySubtitle           string                `json:"displaySubtitle,omitempty"`
	DisplayTitle              string                `json:"displayTitle,omitempty"`
	DoneCuePoint              int                   `json:"doneCuePoint,omitempty"`
	Duration                  int                   `json:"duration,omitempty"`
	Embedded                  VideoEmbedded         `json:"_embedded,omitempty"`
	Entity                    string                `json:"_entity,omitempty"` // no json in v1
	ExpireDate                string                `json:"expireDate,omitempty"`
	HighlightTitle            string                `json:"highlightTitle,omitempty"` // not in v1
	HouseNumber               string                `json:"houseNumber,omitempty"`
	Images                    AmbiguousArray[Image] `json:"images,omitempty"`
	Links                     Links                 `json:"_links,omitempty"`
	Meta                      Meta                  // from v1
	NextVideoCuepoint         int                   `json:"nextVideoCuepoint,omitempty"`
	Participants              []Participant         `json:"participants,omitempty"`
	Playable                  bool                  `json:"playable,omitempty"`
	PubDate                   string                `json:"pubDate,omitempty"`
	SeriesTitle               string                `json:"seriesTitle,omitempty"`
	ShareURL                  string                `json:"shareUrl,omitempty"`
	ShowTitle                 string                `json:"showTitle,omitempty"`
	Status                    Status                `json:"status,omitempty"`
	Tags                      []string              `json:"tags,omitempty"` //not yet added to iview, but they will add it in soon.
	Title                     string                `json:"title,omitempty"`
	Type                      string                `json:"type,omitempty"`
	ID                        string                `json:"id,omitempty"`
	AudioDescriptionsEnabled  bool                  `json:"audioDescriptionsEnabled,omitempty"`
	ClassificationAdvice      string                `json:"classificationAdvice,omitempty"`
	Profiles                  []string              `json:"profiles,omitempty"`
}

func (v *Video) Process() {}

func (v Video) IsCoremediaInteractiveExternal() {}

// ContentType returns the lowercase of entity
func (v Video) ContentType() string {
	return strings.ToLower(v.Entity)
}

// ContentSource returns the source string
func (v Video) ContentSource() string {
	return lib.IView
}

// URI returns the cannonical uri
func (v Video) URI() string {
	return lib.URI(v.ContentSource(), v.ContentType(), v.ID)
}

func (v *Video) Playlist() []PlaylistItem {
	if v == nil {
		return nil
	}
	return v.Embedded.Playlist
}

// ThumbnailImage returns image object can be used as thumbnail for v2 compatibility
func (c Video) ThumbnailImage() (Image, error) {
	for _, image := range c.Images {
		if image.Type != Thumb {
			continue
		}
		if image.Name == EpisodeThumbnail {
			return image, nil
		}
	}
	return Image{}, errors.New("can't find image with name " + EpisodeThumbnail)
}

type Analytics struct {
	DataLayer DataLayer `json:"dataLayer,omitempty"`
	Oztam     Oztam     `json:"oztam,omitempty"`
}

type Oztam struct {
	PublisherID    string `json:"publisherId,omitempty"`
	MediaID        string `json:"mediaId,omitempty"`
	Classification string `json:"classification,omitempty"`
	SeriesName     string `json:"seriesName,omitempty"`
	SeriesID       string `json:"seriesId,omitempty"` // Deprecated in v3
	EpisodeID      string `json:"episodeId,omitempty"`
	EpisodeName    string `json:"episodeName,omitempty"`
	Channel        string `json:"channel,omitempty"`
	Demo1          string `json:"demo1,omitempty"`
	URL            string `json:"url,omitempty"`
	MediaDuration  int    `json:"mediaDuration,omitempty"`
	MediaType      string `json:"mediaType,omitempty"`
}

type DataLayer struct {
	DID                string `json:"d_id,omitempty"`
	DContentType       string `json:"d_contentType,omitempty"`
	DContentSource     string `json:"d_contentSource,omitempty"`
	DURI               string `json:"d_uri,omitempty"`
	DProgramName       string `json:"d_program_name,omitempty"`
	DProgramID         string `json:"d_program_id,omitempty"`
	DSeriesName        string `json:"d_series_name,omitempty"`
	DSeriesID          string `json:"d_series_id,omitempty"`
	DEpisodeName       string `json:"d_episode_name,omitempty"`
	DEpisodeID         string `json:"d_episode_id,omitempty"`
	DLanguage          string `json:"d_language,omitempty"`
	DCanonicalURL      string `json:"d_canonicalUrl,omitempty"`
	DClassification    string `json:"d_classification,omitempty"`
	DMediaDuration     string `json:"d_mediaDuration,omitempty"`
	DCaptionsAvailable string `json:"d_captionsAvailable,omitempty"`
	DStreamType        string `json:"d_streamType,omitempty"`
	DVideoType         string `json:"d_videoType,omitempty"`
	DTitleTitle        string `json:"d_title_title,omitempty"`
	DCategories        string `json:"d_categories,omitempty"`
}

type Status struct {
	Title string `json:"title,omitempty"`
	Theme string `json:"theme,omitempty"`
}

type VideoEmbedded struct {
	Playlist []PlaylistItem `json:"playlist,omitempty"`
}

type PlaylistItem struct {
	Type         string      `json:"type,omitempty"`
	StreamLabels StreamLabel `json:"stream-labels,omitempty"`
	Streams      Streams     `json:"streams,omitempty"`
	// Captions     Captions    `json:"captions,omitempty"`
	Title   string `json:"title,omitempty"`
	Warning string `json:"warning,omitempty"`
}

// TODO: Remove this field totally. Adi confirmed this fields is legacy one on iview, and terminus can remove it. Need wait to see it won't cause any problems
// type Captions struct {
// 	SrcVtt string `json:"src-vtt,omitempty"`
// 	Live   string `json:"live,omitempty"`
// }

type Streams struct {
	Mpegdash    Stream `json:"mpegdash,omitempty"` // not in v1
	Fairplay    Stream `json:"fairplay,omitempty"` // not in v1
	Hls         Stream `json:"hls,omitempty"`
	Hds         Stream `json:"hds,omitempty"` // deprecated in v3
	Progressive Stream `json:"progressive,omitempty"`
}

type Stream struct {
	Sd         string `json:"sd,omitempty"`
	SdLow      string `json:"sd-low,omitempty"`
	Protected  bool   `json:"protected,omitempty"`
	Bitrate720 string `json:"720,omitempty"`
}

type StreamLabel struct {
	Sd         string `json:"sd,omitempty"`
	SdLow      string `json:"sd-low,omitempty"`
	Bitrate720 string `json:"720,omitempty"`
}

func GetVideoEndpoint(id string, meta ContentMeta) (string, error) {
	return Endpoint(VideoEntity, id, meta)
}
