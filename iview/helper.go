package iview

// GetIncludes returns the string for the includes url param that goes with all iview requests
func GetIncludes(entity Entity) string {
	//IMPORTANT:  The 'include' params not only include new fields, but ALSO changes date formats to ISO8601
	switch entity {
	case ShowEntity:
		return "expireDate,misspellings,pubDate,playable,updated,videoCount,phrases"
	case SeriesEntity:
		return "expireDate,pubDate,seriesNumber,videoCount"
	case VideoEntity:
		return "expireDate,pubDate"
	default: //case CollectionEntity: We want to return every possible type as collections can contain anything.
		return "expireDate,misspellings,playable,pubDate,seriesNumber,updated,videoCount"
	}
}

// GetEmbeds returns the string for the embeds url param that goes with some iview requests
func GetEmbeds(entity Entity) string {
	if entity == ShowEntity {
		return "seriesList,highlightVideo,selectedSeries"
	}

	return ""
}
