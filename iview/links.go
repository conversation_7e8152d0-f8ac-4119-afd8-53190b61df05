package iview

import "fmt"

// Links contains all the different links that all the iview v2 models have
type Links struct {
	//All ID's have been commented out as they typechange from string to integer when 0...
	Alert          AlertLink       `json:"alert,omitempty"`          // Show
	Categories     []CategoryItem  `json:"categories,omitempty"`     // Show,Video
	Deeplink       Link            `json:"deeplink,omitempty"`       // Show,Series,Video
	HighlightVideo VideoLink       `json:"highlightVideo,omitempty"` // Show
	NextEpisode    EpisodeLink     `json:"nextEpisode,omitempty"`    // Video
	Related        Link            `json:"related,omitempty"`        // Show
	Self           Link            `json:"self,omitempty"`           // Show,Series,Video
	SelectedSeries Link            `json:"selectedSeries,omitempty"` // Show
	Series         Link            `json:"series,omitempty"`         // Video
	SeriesList     Link            `json:"seriesList,omitempty"`     // Show,Video
	Show           Link            `json:"show,omitempty"`           // Series,SeriesLinkItem
	Stream         Link            `json:"stream,omitempty"`         // Show
	Variations     []LinkVariation `json:"variations,omitempty"`     // TODO: Remove if unused
	Trailer        *Trailer        `json:"trailer,omitempty"`        // Show
}

type Link struct {
	ID    interface{} `json:"id,omitempty"` // used by deeplink in channel(category)
	Href  string      `json:"href,omitempty"`
	Title string      `json:"title,omitempty"`
}

type EpisodeLink struct {
	Link
	SeriesTitle string `json:"seriesTitle,omitempty"`
}

type VideoLink struct {
	Link
	PlayTitle string `json:"playTitle,omitempty"`
}

type BuyLinks struct {
	Apple     string `json:"apple,omitempty"`
	AppleITMS string `json:"appleITMS,omitempty"`
	Google    string `json:"google,omitempty"`
}

type AlertLink struct {
	ID      interface{} `json:"id,omitempty"` // used by deeplink in channel(category)
	Title   string      `json:"title,omitempty"`
	Message string      `json:"message,omitempty"`
	Link    Link        `json:"link,omitempty"`
	Href    string      `json:"href,omitempty"` // Keep it to make non breaking changes in GraphQL
}

func (l Link) ContentID() string {
	return fmt.Sprintf("%v", l.ID)
}

type LinkVariation struct {
	ID   string   `json:"id,omitempty"`
	Href string   `json:"href,omitempty"`
	Tags []string `json:"tags,omitempty"`
}
