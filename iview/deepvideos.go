package iview

import "errors"

/*
	ShowVideo & ShowSeriesVideo refers to content stored inside the 'deeplink' of a video. It has the video data, along with other fields.
	There are 2 types of deeplinks, depending on if the video is of type "feature" or "episode". These 2 deeplinks
	have different endpoints....  please refer to these examples here:

	type "feature": https://api.iview.abc.net.au/v2/video/NS1865H001S00
	deeplink: /show/7-30-special-the-face-of-australias-drought-crisis/video/NS1865H001S00

	type "episode": https://api.iview.abc.net.au/v2/video/ZX9184A117S00
	deeplink: /show/day-my-butt-went-psycho/series/1/video/ZX9184A117S00
*/
type ShowVideo struct {
	ID                        int               `json:"id,omitempty"`
	Slug                      string            `json:"slug,omitempty"`
	Type                      string            `json:"type,omitempty"`
	Title                     string            `json:"title,omitempty"`
	DisplayTitle              string            `json:"displayTitle,omitempty"`
	Description               string            `json:"description,omitempty"`
	Tags                      []string          `json:"tags,omitempty"`
	Theme                     string            `json:"theme,omitempty"`
	Duration                  int               `json:"duration,omitempty"`
	DisplayDuration           string            `json:"displayDuration,omitempty"`
	DisplayDurationAccessible string            `json:"displayDurationAccessible,omitempty"`
	RelatedLinks              []RelatedLink     `json:"relatedLinks,omitempty"`
	ShareURL                  string            `json:"shareUrl,omitempty"`
	Availability              string            `json:"availability,omitempty"`
	Captions                  bool              `json:"captions,omitempty"`
	CaptionsOnAkamai          bool              `json:"captionsOnAkamai,omitempty"`
	Updated                   string            `json:"updated,omitempty"`
	Entity                    string            `json:"_entity,omitempty"`
	Links                     Links             `json:"_links,omitempty"`
	Embedded                  ShowVideoEmbedded `json:"_embedded,omitempty"`
	Images                    []Image           `json:"images,omitempty"`
}

func (s *ShowVideo) Process() {}

// ThumbnailImage returns image object can be used as thumbnail for v2 compatibility
func (c ShowVideo) ThumbnailImage() (Image, error) {
	for _, image := range c.Images {
		if image.Type != Thumb {
			continue
		}
		if image.Name == SeriesThumbnail {
			return image, nil
		}
	}
	return Image{}, errors.New("can't find image with name " + EpisodeThumbnail)
}

type ShowVideoEmbedded struct {
	HighlightVideo Video `json:"highlightVideo,omitempty"`
}

type ShowSeriesVideo struct {
	ID           int                     `json:"id,omitempty"`
	Slug         string                  `json:"slug,omitempty"`
	Type         string                  `json:"type,omitempty"`
	Title        string                  `json:"title,omitempty"`
	DisplayTitle string                  `json:"displayTitle,omitempty"`
	Description  string                  `json:"description,omitempty"`
	Tags         []string                `json:"tags,omitempty"`
	Theme        string                  `json:"theme,omitempty"`
	EpisodeCount int                     `json:"episodeCount,omitempty"`
	SocialLinks  SocialLinks             `json:"socialLinks,omitempty"`
	RelatedLinks []RelatedLink           `json:"relatedLinks,omitempty"`
	ShareURL     string                  `json:"shareUrl,omitempty"`
	Updated      string                  `json:"updated,omitempty"`
	Entity       string                  `json:"_entity,omitempty"`
	Links        Links                   `json:"_links,omitempty"`
	Embedded     ShowSeriesVideoEmbedded `json:"_embedded,omitempty"`
	Images       []Image                 `json:"images,omitempty"`
}

func (s *ShowSeriesVideo) Process() {}

// ThumbnailImage returns image object can be used as thumbnail for v2 compatibility
func (c ShowSeriesVideo) ThumbnailImage() (Image, error) {
	for _, image := range c.Images {
		if image.Type != Thumb {
			continue
		}
		if image.Name == SeriesThumbnail {
			return image, nil
		}
	}
	return Image{}, errors.New("can't find image with name " + EpisodeThumbnail)
}

type ShowSeriesVideoEmbedded struct {
	HighlightVideo Video            `json:"highlightVideo,omitempty"`
	SelectedSeries Series           `json:"selectedSeries,omitempty"`
	SeriesList     []SeriesListItem `json:"seriesList,omitempty"`
}

type SocialLinks struct {
	Title string           `json:"title,omitempty"`
	Items []SocialLinkItem `json:"items,omitempty"`
}

type SocialLinkItem struct {
	ID    string `json:"id,omitempty"`
	Title string `json:"title,omitempty"`
	Href  string `json:"href,omitempty"`
}

type RelatedLink struct {
	Title string `json:"title,omitempty"`
	Href  string `json:"href,omitempty"`
}

//GetShowSeriesVideoEndpoint... deeplink for /video/ZX9184A117S00 is /show/day-my-butt-went-psycho/series/1/video/ZX9184A117S00
func GetShowSeriesVideoEndpoint(id string, meta ContentMeta) (string, error) {
	return Endpoint(VideoEntity, id, meta)
}

//GetShowVideoEndpoint... deeplink for /video/NS1865H001S00 is /show/7-30-special-the-face-of-australias-drought-crisis/video/NS1865H001S00
func GetShowVideoEndpoint(id string, meta ContentMeta) (string, error) {
	return Endpoint(VideoEntity, id, meta)
}
