package iview

import (
	"fmt"
	"strings"
)

type ImageType = string

const (
	Thumb           ImageType = "thumb"
	Portrait        ImageType = "portrait"
	Character1      ImageType = "character-one"
	Character2      ImageType = "character-two"
	PortraitTitled  ImageType = "portrait-titled"
	BannerLargeType ImageType = "banner-large"
	BannerSmallType ImageType = "banner-small"
)

type ImageName = string

const (
	SeriesThumbnail  ImageName = "seriesThumbnail"
	EpisodeThumbnail ImageName = "episodeThumbnail"
	BannerLarge      ImageName = "bannerLarge"
	BannerSmall      ImageName = "bannerSmall"
)

type Image struct {
	URL               string `json:"url,omitempty"`
	Name              string `json:"name,omitempty"`
	Type              string `json:"type,omitempty"`
	AspectRatio       string `json:"aspectRatio,omitempty"`
	Width             int    `json:"width,omitempty"`
	Height            int    `json:"height,omitempty"`
	AltText           string `json:"altText,omitempty"` // Category.Banner
	Link              string `json:"link,omitempty"`    // Category.Banner
	Entity            string `json:"_entity"`
	AccessibilityText string `json:"accessibilityText,omitempty"` // Category.Images
	Source            string `json:"_source,omitempty"`
}

// ContentType returns the lowercase of entity
func (i Image) ContentType() string {
	return strings.ToLower(i.Entity)
}

func (i Image) IsZero() bool {
	return i.URL == ""
}

// ToV2PortraitsType convert iView V3 portraits type to v2 for backward compatibility.
func ToV2PortraitsType(typ string) (string, error) {
	switch typ {
	case Portrait:
		return "portrait", nil
	case Character1:
		return "character1", nil
	case Character2:
		return "character2", nil
	case PortraitTitled:
		return "portraitTitled", nil
	default:
		return typ, fmt.Errorf("unsupported in iView V2 %s", typ)
	}
}
