package lib

import (
	"fmt"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"unsafe"

	"github.com/modern-go/reflect2"
)

type fieldInfo struct {
	field          reflect2.StructField
	anonymousField reflect2.StructField
	opts           map[string]struct{}
}

// ParseParams parses url.Values and fill the given interface base on tag define.
// Naming priority: url > json > field name
// **NOTE:** It only support one level struct now
func ParseParams(params url.Values, s interface{}) error {
	typ := reflect2.TypeOf(s)
	if typ.Kind() != reflect.Ptr {
		return fmt.Errorf("Type should be pointer of struct but actual type is: %s", typ.String())
	}
	typ = typ.(reflect2.PtrType).Elem()
	if typ.Kind() != reflect.Struct {
		return fmt.Errorf("Type should be pointer of struct but actual type is: %s", typ.String())
	}
	dataType := typ.(reflect2.StructType)
	ptr := reflect2.PtrOf(s)
	typeFields := fields(dataType)
	return fillStruct(ptr, typeFields, params, false)
}

// ParseParamsStrict parses url.Values and fill the given interface base on tag define. It will return error if there are unsupported fields.
// Naming priority: url > json > field name
// **NOTE:** It only support one level struct now
func ParseParamsStrict(params url.Values, s interface{}) error {
	typ := reflect2.TypeOf(s)
	if typ.Kind() != reflect.Ptr {
		return fmt.Errorf("Type should be pointer of struct but actual type is: %s", typ.String())
	}
	typ = typ.(reflect2.PtrType).Elem()
	if typ.Kind() != reflect.Struct {
		return fmt.Errorf("Type should be pointer of struct but actual type is: %s", typ.String())
	}
	dataType := typ.(reflect2.StructType)
	ptr := reflect2.PtrOf(s)
	typeFields := fields(dataType)
	return fillStruct(ptr, typeFields, params, true)
}

func fields(dataType reflect2.StructType) map[string]fieldInfo {
	typeFields := make(map[string]fieldInfo)
	numField := dataType.NumField()
	for i := 0; i < numField; i++ {
		field := dataType.Field(i)
		urlKey, ok := field.Tag().Lookup("url")
		if !ok {
			urlKey, ok = field.Tag().Lookup("json")
			if !ok {
				urlKey = field.Name()
			}
		} else if urlKey == "-" {
			continue
		}
		if field.Anonymous() {
			subType := field.Type()
			if subType.Kind() == reflect.Ptr {
				subType = subType.(reflect2.PtrType).Elem()
			}
			if subStruct, ok := subType.(reflect2.StructType); ok {
				subFields := fields(subStruct)
				for urlKey, subField := range subFields {
					subField.anonymousField = field
					typeFields[urlKey] = subField
				}
			}
		} else {
			opts := strings.Split(urlKey, ",")
			urlKey = opts[0]
			opts = opts[1:]
			optMap := make(map[string]struct{})
			for _, opt := range opts {
				optMap[opt] = struct{}{}
			}
			typeFields[urlKey] = fieldInfo{
				field: field,
				opts:  optMap,
			}
		}
	}
	return typeFields
}

func fillStruct(ptr unsafe.Pointer, typeFields map[string]fieldInfo, params url.Values, strict bool) error {
	for urlKey := range params {
		field, ok := typeFields[urlKey]
		if !ok {
			if strict {
				return fmt.Errorf("Unsupported param %s", urlKey)
			}
			continue
		}
		urlValue := params.Get(urlKey)
		if urlValue == "" {
			continue
		}
		fieldType := field.field.Type()
		kind := fieldType.Kind()
		setPtr := ptr
		if field.anonymousField != nil {
			setPtr = field.anonymousField.UnsafeGet(ptr)
		}
		switch kind {
		case reflect.String:
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&urlValue))
		case reflect.Int:
			intVal, err := strconv.Atoi(urlValue)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&intVal))
		case reflect.Int8:
			intVal, err := strconv.ParseInt(urlValue, 10, 8)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := int8(intVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Int16:
			intVal, err := strconv.ParseInt(urlValue, 10, 16)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := int16(intVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Int32:
			intVal, err := strconv.ParseInt(urlValue, 10, 32)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := int32(intVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Int64:
			intVal, err := strconv.ParseInt(urlValue, 10, 64)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&intVal))
		case reflect.Uint:
			intVal, err := strconv.ParseUint(urlValue, 10, strconv.IntSize)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := uint(intVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Uint8:
			intVal, err := strconv.ParseUint(urlValue, 10, 8)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := uint8(intVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Uint16:
			intVal, err := strconv.ParseUint(urlValue, 10, 16)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := uint16(intVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Uint32:
			intVal, err := strconv.ParseUint(urlValue, 10, 32)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := uint32(intVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Uint64:
			intVal, err := strconv.ParseUint(urlValue, 10, 64)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&intVal))
		case reflect.Float32:
			fVal, err := strconv.ParseFloat(urlValue, 32)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			valToSet := float32(fVal)
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&valToSet))
		case reflect.Float64:
			fVal, err := strconv.ParseFloat(urlValue, 64)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&fVal))
		case reflect.Bool:
			bVal, err := strconv.ParseBool(urlValue)
			if err != nil {
				return fmt.Errorf("Error on converting query %s with value %s to type %s: %s", urlKey, urlValue, fieldType.String(), err.Error())
			}
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&bVal))
		case reflect.Slice, reflect.Array:
			split := ""
			if _, ok := field.opts["comma"]; ok {
				split = ","
			} else if _, ok := field.opts["space"]; ok {
				split = " "
			} else if _, ok := field.opts["semicolon"]; ok {
				split = ";"
			}
			var array []string
			if split != "" {
				array = strings.Split(urlValue, split)
			} else {
				array = params[urlKey]
			}
			field.field.UnsafeSet(setPtr, unsafe.Pointer(&array))
		default:
			return fmt.Errorf("Unsupported type %s on field %s", fieldType.String(), field.field.Name())
		}
	}
	return nil
}
